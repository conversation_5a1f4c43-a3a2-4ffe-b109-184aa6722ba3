[versions]
agp = "8.10.1"
appcompat = "1.7.1"
activity = "1.10.1"
awsAndroidSdkS3 = "2.79.0"
datastorePreferences = "1.1.7"
datatower = "3.2.0"
desugarLibrary = "1.1.5"
flycotablayout = "3.0.0"
fragment = "1.8.8"
annotations = "26.0.2"
collection = "1.5.0"
leakcanaryAndroid = "2.14"
pictureselector = "v3.11.2"
runtime = "1.8.2"
coreRuntime = "2.2.0"
kotlin = "2.1.21"
coreKtx = "1.16.0"
activityCompose = "1.10.1"
composeBom = "2025.05.01"
accompanist = "0.37.3"
kotlinxCoroutinesAndroid = "1.10.2"
kotlinxCoroutinesCore = "1.10.2"
lifecycleViewmodelAndroid = "2.9.0"
lifecycleRuntimeKtx = "2.9.0"
lifecycleLivedataKtx = "2.9.0"
lifecycleViewmodelKtx = "2.9.0"
lifecycleProcess = "2.9.0"
navigationFragmentKtx = "2.9.0"
navigationUiKtx = "2.9.0"
supertextview = "2.4.6"
svgaplayerAndroid = "2.6.1"
uiGraphics = "1.8.2"
coilCompose = "2.7.0"
constraintlayoutCompose = "1.1.1"
navigationCompose = "2.9.0"
foundation = "1.8.2"
material = "1.8.2"
materialVersion = "1.12.0"
recyclerview = "1.4.0"
flexbox = "3.0.0"
constraintlayout = "2.2.1"
viewbinding = "8.10.1"
swiperefreshlayout = "1.1.0"
loader = "1.1.0"
datastoreCore = "1.1.7"
room = "2.7.1"
ksp = "2.1.20-1.0.31"

playServicesAuth = "21.3.0"
firebaseBom = "33.14.0"
billing = "7.1.1"
facebookAndroidSdk = "18.0.3"

junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"

okhttp = "4.12.0"
retrofit = "3.0.0"
gson = "2.13.1"
permissionx = "1.8.1"
glide = "4.16.0"
glideTransformations = "4.3.0"
logger = "2.2.0"
eventbus = "3.3.1"
# 2.x版本不支持 armeabi-v7a构架
mmkv = "1.3.7"
ucrop = "2.2.10"
immersionbarKtx = "3.2.2"
dsladapter = "7.0.0"
dsltablayout = "3.5.5"
xdmapLocationSearch = "10.0.700_loc6.4.5_sea9.7.2"
xpopup = "2.10.0"
moshi = "1.15.2"
wheelviewVersion = "4.1.14"
rtcFullSdk = "4.5.2"
permissions = "21.3"
preference = "1.2.1"
rongYunSdk = "5.7.5"
gsyVideoPlayer = "v9.0.0-release-jitpack"
splashscreen = "1.0.1"
playServicesMeasurementApi = "22.4.0"
firebaseCrashlyticsKtx = "19.4.3"

clarityPotion = "1.0.6"
googleService = "4.4.2"
crashlytics = "3.0.3"
kyleduoSwitchbutton = "2.1.0"
bannerviewpager= "3.5.12"
andRatingBar= "1.0.6"
installreferrer = "2.2"
afAndroidSdk = "6.17.0"
oaid = "6.12.3"
emoji2 = "1.5.0"

[libraries]
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "activity" }
androidx-collection = { module = "androidx.collection:collection", version.ref = "collection" }
androidx-collection-jvm = { module = "androidx.collection:collection-jvm", version.ref = "collection" }
androidx-collection-ktx = { module = "androidx.collection:collection-ktx", version.ref = "collection" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastorePreferences" }
androidx-datastore-preferences-core = { module = "androidx.datastore:datastore-preferences-core", version.ref = "datastorePreferences" }
androidx-fragment = { group = "androidx.fragment", name = "fragment", version.ref = "fragment" }
androidx-fragment-ktx = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragment" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "foundation" }
androidx-loader = { module = "androidx.loader:loader", version.ref = "loader" }
androidx-material = { module = "androidx.compose.material:material", version.ref = "material" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
androidx-room-common = { module = "androidx.room:room-common", version.ref = "room" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-paging = { module = "androidx.room:room-paging", version.ref = "room" }
androidx-runtime = { module = "androidx.compose.runtime:runtime", version.ref = "runtime" }
androidx-viewbinding = { module = "androidx.databinding:viewbinding", version.ref = "viewbinding" }
annotations = { module = "org.jetbrains:annotations", version.ref = "annotations" }
aws-android-sdk-mobile-client = { module = "com.amazonaws:aws-android-sdk-mobile-client", version.ref = "awsAndroidSdkS3" }
aws-android-sdk-s3 = { module = "com.amazonaws:aws-android-sdk-s3", version.ref = "awsAndroidSdkS3" }
billing = { module = "com.android.billingclient:billing", version.ref = "billing" }
billing-ktx = { module = "com.android.billingclient:billing-ktx", version.ref = "billing" }
coilCompose = { module = "io.coil-kt:coil-compose", version.ref = "coilCompose" }
datatower = { module = "ai.datatower:core", version.ref = "datatower" }
desugar-library = { module = "com.google.android.desugar:desugar-library", version.ref = "desugarLibrary" }
facebook-android-sdk = { module = "com.facebook.android:facebook-android-sdk", version.ref = "facebookAndroidSdk" }
facebook-core-sdk = { module = "com.facebook.android:facebook-core", version.ref = "facebookAndroidSdk" }
facebook-marketing-sdk = { module = "com.facebook.android:facebook-marketing", version.ref = "facebookAndroidSdk" }
firebase-analytics-ktx = { module = "com.google.firebase:firebase-analytics-ktx" }
firebase-auth-ktx = { module = "com.google.firebase:firebase-auth-ktx" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
flycotablayout = { module = "io.github.h07000223:flycoTabLayout", version.ref = "flycotablayout" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
af-android-sdk = { module = "com.appsflyer:af-android-sdk", version.ref = "afAndroidSdk" }
oaid = { module = "com.appsflyer:oaid", version.ref = "oaid" }
leakcanary-android = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "leakcanaryAndroid" }

lucksiege-ucrop = { module = "io.github.lucksiege:ucrop", version.ref = "pictureselector" }
pictureselector = { module = "io.github.lucksiege:pictureselector", version.ref = "pictureselector" }
lucksiege-camerax = { module = "io.github.lucksiege:camerax", version.ref = "pictureselector" }
lucksiege-compress = { module = "io.github.lucksiege:compress", version.ref = "pictureselector" }
squareup-moshi = { module = "com.squareup.moshi:moshi", version.ref = "moshi" }
squareup-moshiKt = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshi" }
squareup-moshiCodegen = { module = "com.squareup.moshi:moshi-kotlin-codegen", version.ref = "moshi" }
retrofitMoshi = { module = "com.squareup.retrofit2:converter-moshi", version.ref = "retrofit" }


#accompanist
accompanist-permissions = { group = "com.google.accompanist", name = "accompanist-permissions", version.ref = "accompanist" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "playServicesAuth" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
supertextview = { module = "com.github.lygttpod:SuperTextView", version.ref = "supertextview" }
svgaplayer-android = { module = "com.github.yyued:SVGAPlayer-Android", version.ref = "svgaplayerAndroid" }
ui-graphics = { module = "androidx.compose.ui:ui-graphics", version.ref = "uiGraphics" }
logger = { module = "com.orhanobut:logger", version.ref = "logger" }
mmkv = { module = "com.tencent:mmkv-static", version.ref = "mmkv" }
ucrop = { module = "com.github.yalantis:ucrop", version.ref = "ucrop" }

glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-transformations = { module = "jp.wasabeef:glide-transformations", version.ref = "glideTransformations" }
material = { group = "com.google.android.material", name = "material", version.ref = "materialVersion" }

androidx-core = { module = "androidx.core:core", version.ref = "coreKtx" }
androidx-core-runtime = { module = "androidx.arch.core:core-runtime", version.ref = "coreRuntime" }
androidx-lifecycle-common-java8 = { module = "androidx.lifecycle:lifecycle-common-java8", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-livedata = { module = "androidx.lifecycle:lifecycle-livedata", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-livedata-core-ktx = { module = "androidx.lifecycle:lifecycle-livedata-core-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-runtime = { module = "androidx.lifecycle:lifecycle-runtime", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-savedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-android = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-android", version.ref = "lifecycleViewmodelAndroid" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigationUiKtx" }

constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
swiperefreshlayout = { group = "androidx.swiperefreshlayout", name = "swiperefreshlayout", version.ref = "swiperefreshlayout" }
flexbox = { module = "com.google.android.flexbox:flexbox", version.ref = "flexbox" }

androidx-datastore-core = { module = "androidx.datastore:datastore-core", version.ref = "datastoreCore" }

dsladapter = { module = "com.github.angcyo:DslAdapter", version.ref = "dsladapter" }
dsltablayout = { module = "com.github.angcyo:DslTabLayout", version.ref = "dsltablayout" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
immersionbar = { module = "com.geyifeng.immersionbar:immersionbar", version.ref = "immersionbarKtx" }
immersionbar-components = { module = "com.geyifeng.immersionbar:immersionbar-components", version.ref = "immersionbarKtx" }
immersionbar-ktx = { module = "com.geyifeng.immersionbar:immersionbar-ktx", version.ref = "immersionbarKtx" }

permissionx = { module = "com.guolindev.permissionx:permissionx", version.ref = "permissionx" }
xdmap-location-search = { module = "com.amap.api:3dmap-location-search", version.ref = "xdmapLocationSearch" }
xpopup = { group = "com.github.li-xiaojun", name = "XPopup", version.ref = "xpopup" }
androidpicker-addresspicker = { module = "com.github.gzu-liyujiang.AndroidPicker:AddressPicker", version.ref = "wheelviewVersion" }
androidpicker-wheelview = { module = "com.github.gzu-liyujiang.AndroidPicker:WheelView", version.ref = "wheelviewVersion" }

rtcFull = { module = "io.agora.rtc:full-sdk", version.ref = "rtcFullSdk" }
xxPermission = { module = "com.github.getActivity:XXPermissions", version.ref = "permissions" }
androidx-preference = { group = "androidx.preference", name = "preference", version.ref = "preference" }
rongYun = { module = "cn.rongcloud.sdk:im_kit", version.ref = "rongYunSdk" }
gsyVideoPlayer = { module = "com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer", version.ref = "gsyVideoPlayer" }
clarityPotion = { module = "com.github.ssseasonnn:ClarityPotion", version.ref = "clarityPotion" }
splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "splashscreen" }
play-services-measurement-api = { group = "com.google.android.gms", name = "play-services-measurement-api", version.ref = "playServicesMeasurementApi" }
firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx", version.ref = "firebaseCrashlyticsKtx" }
kyleduo-switchbutton = { group = "com.kyleduo.switchbutton", name = "library", version.ref = "kyleduoSwitchbutton" }
bannerViewPager = { module = "com.github.zhpanvip:bannerviewpager", version.ref = "bannerviewpager" }
andRatingBar = { module = "com.github.wdsqjq:AndRatingBar", version.ref = "andRatingBar" }
androidx-emoji2 = { group = "androidx.emoji2", name = "emoji2", version.ref = "emoji2" }
lifecycleProcess = { module = "androidx.lifecycle:lifecycle-process", version.ref = "lifecycleProcess" }
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
google-gradle-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }
google-services = { id = 'com.google.gms.google-services', version.ref = "googleService" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }
