<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_5"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.cardview.widget.CardView
        android:id="@+id/container_card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_2"
        android:layout_marginTop="@dimen/dp_10"
        app:cardBackgroundColor="@color/color_card_background"
        app:cardCornerRadius="@dimen/dp_10">

        <RelativeLayout
            android:id="@+id/rl_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/dp_10"
            android:paddingStart="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_10">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="@dimen/dp_38"
                android:layout_height="@dimen/dp_38"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@mipmap/ic_diamond_recharge_level3" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_toEndOf="@id/iv_icon"
                android:orientation="vertical">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_diamond_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="@dimen/dp_3"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_15"
                    android:textStyle="bold"
                    tools:text="420钻石" />

                <LinearLayout
                    android:id="@+id/ll_give_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    app:layout_constraintStart_toStartOf="@id/tv_diamond_value"
                    app:layout_constraintTop_toBottomOf="@id/tv_diamond_value">

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_level"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="LV10 Bonus:"
                        android:textColor="@color/white_30"
                        android:textSize="@dimen/sp_11" />

                    <com.heart.heartmerge.i18n.I18nTextView
                        android:id="@+id/tv_give_diamond"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_5"
                        android:text="30"
                        android:textColor="@color/color_D195FF"
                        android:textSize="@dimen/sp_11" />

                    <ImageView
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18"
                        android:background="@mipmap/ic_diamond_purple" />
                </LinearLayout>
            </LinearLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/btn_amount"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_24"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@drawable/shape_9f2af8_50"
                android:gravity="center"
                android:minWidth="@dimen/dp_60"
                android:paddingHorizontal="@dimen/dp_6"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_11"
                tools:text="¥50" />

        </RelativeLayout>
    </androidx.cardview.widget.CardView>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        android:layout_margin="-5dp"
        android:background="@mipmap/ic_recharge_label"
        android:elevation="100dp"
        android:paddingHorizontal="@dimen/dp_5"
        android:paddingTop="@dimen/dp_3"
        android:text="赠送2张匹配卡"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10" />
</RelativeLayout>