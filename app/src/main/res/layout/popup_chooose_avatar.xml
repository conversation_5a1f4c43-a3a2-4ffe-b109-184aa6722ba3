<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_bottom_popup"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_take_picture"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/photograph"
        android:visibility="gone"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0_1"
        android:background="#70746988" />

    <TextView
        android:id="@+id/tv_gallery"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/select_from_album"
        android:textColor="@color/color_white"
        android:textSize="@dimen/sp_16" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_10"
        android:background="#70201831" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_58"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_F53D3D"
        android:textSize="@dimen/sp_16" />
</LinearLayout>