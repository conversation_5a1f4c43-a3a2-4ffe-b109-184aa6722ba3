<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/xRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp_10"
        app:hasRecyclerView="true" />

    <ImageView
        android:id="@+id/back_top"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="16dp"
        android:src="@mipmap/ic_back_top" />

    <FrameLayout
        android:id="@+id/lock_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:background="@mipmap/bg_home_new_lock"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:elevation="@dimen/dp_10"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp_20"
                android:gravity="center">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="@dimen/dp_77"
                    android:layout_height="@dimen/dp_77"
                    android:layout_centerInParent="true"
                    tools:src="@mipmap/ic_pic_default_oval" />

                <ImageView
                    android:layout_width="@dimen/dp_110"
                    android:layout_height="@dimen/dp_110"
                    android:src="@mipmap/ic_avatar_border" />

            </RelativeLayout>

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_conditions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_15"
                android:text="@string/only_vip_can_see_new_girls"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_14" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_30"
                android:clipChildren="false"
                android:clipToPadding="false">

                <com.heart.heartmerge.i18n.I18nButton
                    android:id="@+id/btn_unlock"
                    style="@style/PrimaryButton"
                    android:text="@string/unlock" />

                <ImageView
                    android:layout_width="@dimen/dp_54"
                    android:layout_height="@dimen/dp_54"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="-25dp"
                    android:layout_marginEnd="@dimen/dp_5"
                    android:elevation="@dimen/dp_10"
                    android:rotation="30"
                    android:src="@mipmap/ic_diamond_big" />
            </RelativeLayout>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>
