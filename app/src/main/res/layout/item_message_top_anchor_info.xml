<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp_16"
    android:background="@drawable/shape_conversation_top_bg"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_10"
    android:paddingVertical="@dimen/dp_12">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/anchor_header"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"
            android:src="@mipmap/ic_default_avatar" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dp_44"
            android:orientation="vertical">

            <TextView
                android:id="@+id/anchor_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/dp_150"
                android:maxLines="1"
                android:ellipsize="middle"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_age"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:background="@drawable/shape_home_anchor_age"
                    android:includeFontPadding="false"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="@dimen/dp_1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    tools:text="20" />

                <TextView
                    android:id="@+id/tv_country"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_4"
                    android:layout_marginTop="@dimen/dp_5"
                    android:background="@drawable/shape_home_anchor_price"
                    android:includeFontPadding="false"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:paddingVertical="@dimen/dp_1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    tools:text="ID" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_top_follow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/shape_call_top_follow_btn_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_12"
            android:paddingVertical="@dimen/dp_8"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_top_follow"
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_7"
                android:src="@mipmap/ic_call_top_follow" />

            <com.heart.heartmerge.i18n.I18nTextView
                android:id="@+id/tv_top_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:includeFontPadding="false"
                android:text="@string/label_cancel_follow"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10" />

        </LinearLayout>
    </LinearLayout>

    <com.heart.heartmerge.i18n.I18nTextView
        android:id="@+id/anchor_signature"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_images"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/anchor_image1"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/anchor_image2"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/anchor_image3"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/anchor_image4"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/anchor_image5"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>