<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.allen.library.shape.ShapeConstraintLayout
        android:id="@+id/cl_center"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_330"
        android:layout_marginVertical="@dimen/dp_40"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeCornersRadius="@dimen/dp_24"
        app:shapeSolidColor="@color/white">

        <com.heart.heartmerge.i18n.I18nTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_35"
            android:text="@string/main_platform_rule_title"
            android:textColor="@color/color_F53D3D"
            android:textSize="@dimen/sp_24"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_5"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@id/btn_know"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="@dimen/dp_3"
                    android:text="@string/main_platform_rule_content"
                    android:textColor="@color/color_100821"
                    android:textSize="@dimen/sp_12" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_content_one"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_5"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:textColor="@color/color_100821"
                    android:textSize="@dimen/sp_12" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_content_two"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_100821"
                    android:textSize="@dimen/sp_12" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:id="@+id/tv_content_three"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_100821"
                    android:textSize="@dimen/sp_12" />
            </LinearLayout>

        </ScrollView>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_110"
            android:background="@drawable/shape_platform_rule_shadow"
            app:layout_constraintBottom_toBottomOf="parent" />

        <com.heart.heartmerge.i18n.I18nButton
            android:id="@+id/btn_know"
            style="@style/PrimaryButton"
            android:layout_height="@dimen/dp_44"
            android:layout_marginHorizontal="@dimen/dp_35"
            android:layout_marginBottom="@dimen/dp_20"
            android:background="@drawable/bg_dialog_platform_sure_btn"
            android:text="@string/text_button_know"
            app:layout_constraintBottom_toBottomOf="parent" />

    </com.allen.library.shape.ShapeConstraintLayout>

    <ImageView
        android:layout_width="@dimen/dp_90"
        android:layout_height="@dimen/dp_70"
        android:layout_marginTop="-35dp"
        android:src="@mipmap/ic_dialog_warning"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/cl_center" />
</androidx.constraintlayout.widget.ConstraintLayout>

