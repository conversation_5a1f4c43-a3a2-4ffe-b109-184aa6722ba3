<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bdc.android.library.widget.XToolbar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:x_title="@string/reward_tasks" />

    <com.bdc.android.library.refreshlayout.XRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        app:hasRecyclerView="false">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/career_tasks"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    android:textSize="@dimen/sp_16" />

                <com.bdc.android.library.refreshlayout.XRecyclerView
                    android:id="@+id/rv_career_task"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:background="@drawable/shape_card_background"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_task" />

                <com.heart.heartmerge.i18n.I18nTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:text="@string/daily_tasks"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    android:textSize="@dimen/sp_16" />

                <com.bdc.android.library.refreshlayout.XRecyclerView
                    android:id="@+id/rv_daily_task"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:visibility="gone"
                    android:background="@drawable/shape_card_background"
                    android:paddingVertical="@dimen/dp_4"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_task" />
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </com.bdc.android.library.refreshlayout.XRefreshLayout>
</LinearLayout>