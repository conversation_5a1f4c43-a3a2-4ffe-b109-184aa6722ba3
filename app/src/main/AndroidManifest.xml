<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!--声网可选权限-->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!-- 声网 对于 Android 12.0 及以上且集成 v4.1.0 以下 SDK 的设备，还需要添加以下权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!-- 声网 对于 Android 12.0 及以上设备，还需要添加以下权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission android:name="com.android.vending.BILLING" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.HeartMerge"
        tools:replace="android:fullBackupContent,android:dataExtractionRules"
        tools:targetApi="31">

        <provider
            android:name="com.facebook.internal.FacebookInitProvider"
            android:authorities="com.heart.heartmerge.FacebookInitProvider"
            android:exported="false"
            android:initOrder="100"
            tools:node="remove" />


        <!--        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>-->
        <!--        <meta-data-->
        <!--            android:name="com.facebook.sdk.ClientToken"-->

        <!--            android:value="13f750d478afff69a146708197070619" />-->

        <activity
            android:name=".ui.activities.WelcomeActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="payment-success"
                    android:scheme="heartmerge" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activities.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.login.LoginActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.login.AgreementActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.login.EmailLoginActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.anchor.detail.AnchorDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.ProfileCompletionActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.MembershipCenterActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.MatchingCardActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.MatchingCardRecordActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.WalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.BackpackActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.InvitationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.PermissionManagerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.SettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.anchor.AnchorVideoActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.WalletTransactionActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.SubscribeHistoryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mingle.MatchingRecordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.anchor.VideoCallSettlementActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.BlacklistActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.message.MyRongConversationActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.AboutActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.CancelAccountActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.CancelAccountSubmitActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.CancelAccountConfirmActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.CancelAccountResultActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.anchor.search.AnchorSearchActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.anchor.detail.VideoPlayActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Translucent" />

        <activity
            android:name=".ui.activities.anchor.IncomingVideoActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.WebViewActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ui.activities.mine.FeedbackActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.anchor.AnchorReportActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />

        <activity
            android:name=".ui.activities.mine.RaffleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.mine.WinningRecordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.BuildProfileActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.ProfileActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.LevelActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.RechargeHistoryActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.DiamondDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.anchor.AnchorRateStarActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.WhoSeeMeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.mine.TaskActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>