package com.heart.heartmerge.extension

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.content.Context
import android.content.res.Resources
import android.util.TypedValue
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.widget.ViewPager2
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.SubscriptionType
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.ViewPagerFmAdapter
import java.net.URL
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


fun String.isEmail(): Boolean {
    val s = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}\$"
    return this.matches(s.toRegex())
}

fun Long.formatDate(): String {
    val date = Date(this)
    val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    return format.format(date)
}

fun Long.formatDateTime(): String {
    val date = Date(this)
    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return format.format(date)
}

fun Long.formatCountdown(): String {
    val totalSeconds = this
    val hours = totalSeconds / 3600
    val remainderSeconds = totalSeconds % 3600
    val minutes = remainderSeconds / 60
    val seconds = remainderSeconds % 60
    return String.format("%02d:%02d:%02d", hours, minutes, seconds)
}

fun String.toTimestamp(): Long {
    return try {
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        format.parse(this)?.time ?: 0L
    } catch (e: Exception) {
        0L
    }
}

fun String.formatAsDashedDate(): String {
    val inputFormat = SimpleDateFormat("yyyyMMdd")
    val outputFormat = SimpleDateFormat("yyyy-MM-dd")
    val date = inputFormat.parse(this)
    val formatted = outputFormat.format(date)
    return formatted
}

fun String.toSubscriptionTitle(): String {
    return when (this) {
        SubscriptionType.WEEKLY.value -> ContextHolder.context.getString(R.string.vip_1_week)
        SubscriptionType.MONTHLY.value -> ContextHolder.context.getString(R.string.vip_1_month)
        SubscriptionType.QUARTERLY.value -> ContextHolder.context.getString(R.string.vip_3_months)
        else -> {
            ""
        }
    }
}

fun String.buildImageUrl(
    thumbnail: Boolean = true, highQuality: Boolean = false, list: Boolean = false
): String {
    if (this.endsWith(".mp4") || this.endsWith(".svga")) {
        return this
    } else {
        val url =
            if (this.startsWith("http") || this.startsWith("https")) return this else "${Constants.ImageSpec.getDomain()}$this"
        return "${url}?=${Constants.ImageSpec.get(thumbnail, highQuality, list)}"
    }
}

fun String.extractS3ObjectKey(): String {
    return URL(this).path.removePrefix("/") // 去掉开头的 `/`
}


fun String.toMD5(): String {
    val bytes = MessageDigest.getInstance("MD5").digest(this.toByteArray())
    return bytes.joinToString("") { "%02x".format(it) }
}


fun Long.toMinutes(): Int {
    return (this / 60000).toInt()
}

inline val Int.dp: Int get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

inline val Int.px: Int get() = (this / Resources.getSystem().displayMetrics.density + 0.5f).toInt()

inline val Int.sp: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP, this.toFloat(), Resources.getSystem().displayMetrics
    )

fun Context.getDimen(id: Int): Int {
    return resources.getDimension(id).toInt()
}

fun ViewPager2.attach(
    manager: FragmentManager,
    lifeCycle: Lifecycle,
    fragments: List<Fragment>,
    isInputEnabled: Boolean = true, //默认可滑动
    isLimitAll: Boolean = false,//默认不加载全部
    onPageSelected: (position: Int) -> Unit
) {
    isUserInputEnabled = isInputEnabled
    if (isLimitAll) {
        offscreenPageLimit = fragments.size
    }
    adapter = ViewPagerFmAdapter(manager, lifeCycle, fragments)
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            onPageSelected(position)
        }
    })
}

// ViewPager2扩展函数，设置当前选中的索引
fun ViewPager2.checkSelected(position: Int) {
    if (currentItem != position) {
        setCurrentItem(position, false)
    }
}

fun View.whatsAppAnimation() {
    // 放大缩小动画
    val scaleX = ObjectAnimator.ofFloat(this, "scaleX", 1f, 1.2f, 1f)
    val scaleY = ObjectAnimator.ofFloat(this, "scaleY", 1f, 1.2f, 1f)

    // 左右摇摆动画，使用旋转动画
    val rotate = ObjectAnimator.ofFloat(this, "rotation", 0f, 10f, -10f, 0f)

    val animatorSet = android.animation.AnimatorSet().apply {
        playTogether(scaleX, scaleY, rotate)
        duration = 700
        interpolator = LinearInterpolator()
    }

    val repeatCount = 3 // 想要运行的总次数
    var currentCount = 0

    animatorSet.addListener(object : AnimatorListenerAdapter() {
        override fun onAnimationEnd(animation: Animator) {
            currentCount++
            if (currentCount <= repeatCount) {
                Thread.sleep(300)
                animatorSet.start() // 重新开始动画
            } else {
                animatorSet.removeAllListeners()
                animatorSet.cancel()
            }
        }
    })
    animatorSet.start()
}
