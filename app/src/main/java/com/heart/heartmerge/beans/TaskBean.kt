package com.heart.heartmerge.beans

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2024/11/9 11:13
 * @description :等级权益Bean
 */
data class TaskBean(
    val id: String = "",
    val missionId: String = "",
//    val title: String = "",
    val type: Int = 0,
    val bonus: String = "",
    val standardValue: Int = 0,
    val canGet: String = "",
    val desc: String = "",
    val toType: Int = 0,
    val current: Int = 0,

    //new api add fields
    val task_id: Int = 0,
    val title: String = "",
    val sub_title: String = "",
    val icon: String = "",
    val task_type: String = "",//任务类型 1一次性任务 2每日任务 3每周任务
    val reward: TaskRewardBean? = null,//奖励信息
    val jump_url: String = "",//跳转url
    val target: String = "",//1内部跳转 2网页跳转
    val task_status: Int = 0,//-1 未完成 1 已完成未领取 2已领取
    val complete_config_json: TaskCompleteConfigBean? = null,
    val user_type: Int = 0,//用户类型 1用户2主播
    val process_type: Int = 0,//0 不显示进度 1显示进度
    val step: Int = 0,//进度
    val level: Int = 0,//等级
) {
    val isCompleted get() = task_status == 1

    val isClaimed get() = task_status == 2

    val hasProgress get() = process_type == 1

    val isUserTask get() = user_type == 1

    data class TaskRewardBean(val diamond_num: Int, val gold_num: Int)

    data class TaskCompleteConfigBean(
        val total: Int,
    )
}

data class TaskCategoryBean(
    val oneTimeMissions: List<TaskBean>, val dailyMissions: List<TaskBean>, val list: List<TaskBean>
)