package com.heart.heartmerge.beans

import com.android.billingclient.api.ProductDetails
import com.google.gson.annotations.SerializedName

data class GoodsBean(
    val id: String,
    val currency: String,
    val originPrice: Double,
    val priceType: String,/*1-钻石消耗,2-会员订阅,3-全部*/
    val imageUrl: String,
    val packageName: String,
    val level: String,
    val createTime: String,

    @SerializedName("expire") var deadline: Long,//首充倒计时截止时间
    var countdown: String,

    // new api add fields
    val orderId: String = "",
    val title: String = "",
    val days: Int = 1,
    val desc: String = "",
    val product_id: String = "",
    val sku: String = "",
    val coin: String = "",
    val gold: Int = 0,
    val initial_coin: String = "",
    val initial_price: String = "",
    val real_price: String = "",
    val free: String = "",
    val level_bonus: String = "",
    val symbol: String = "",
    val symbol_behind: Boolean = false,
    val country: String = "",
    val price: String = "",
    val show_price: String = "",
    val show_initial_price: String = "",
    val discount: Double = 0.0,
    val product_type: Int = 0,//1普通商品 2vip商品 3首充商品
    val stat: Int = 0,//状态1支付中 2支付成功 3支付失败
    val channels: List<ChannelBean>? = null,
    var targetChannel: ChannelBean? = null,
    var createdAt: Long = 0L,
    var country_id: Int = 0,
    //谷歌商品扩展参数
    var googleExtras: ProductDetails? = null
) {
    val isSubscribe get() = product_type == 2

    val hasDiscount get() = discount > 0 && discount < 1

    val hasCountdown get() = deadline > 0 && deadline > System.currentTimeMillis()

    val formattedPrice
        get() = googleExtras?.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.formattedPrice
            ?: googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice ?: show_price

    data class ChannelBean(
        val name: String,
        val payType: Int,//支付类型 1 谷歌 2苹果 30funpay 31pagesmail 32secpay 33allpay 34haipay 35 payssion 36payloco 37 payermax
        val channel: String,
        val currency: String,
        val symbol: String = "",
        val symbol_behind: Boolean = false,
        val country: String = "",
        val price: Int = 0,
        val usd_price: Int = 0,
        val show_price: String = "",
        val show_initial_price: String = "",
        val discount: Double = 0.0,
        val product_type: Int = 0,
        val third_channel_id: Int = 0,
        val icon: String = "",
        val show: Boolean = false
    ) {
        val formattedPrice get() = show_price

        val isGooglePay get() = payType == 1
    }

    companion object {
        fun getSampleInstance() = GoodsBean(
            id = "sample_id",
            currency = "USD",               // 货币单位
            price = "2.99",                 // 显示价格
            originPrice = 4.99,             // 原价
            country = "US",                 // 国家
            priceType = "1",               // 钻石消耗类型
            imageUrl = "",                 // 图片URL
            packageName = "com.heart.heartmerge", // 包名
            level = "1",                   // 等级
            createTime = System.currentTimeMillis().toString(), // 创建时间
            deadline = System.currentTimeMillis(), // 当前时间戳
            countdown = "3600",            // 倒计时
            googleExtras = null            // 谷歌商品信息
        )
    }
}
