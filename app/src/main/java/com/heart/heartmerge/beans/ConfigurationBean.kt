package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
@kotlinx.parcelize.Parcelize
data class ConfigurationBean(
    val messagePrice: String,
    val randomMatchPrice: String,
    val rechargeDiscountLimit: String,
    val newUserRemain: String,
    val extractBasic: String,
    val lottery_10: String,
    val userVideoPrice: String,
    val firstOfferPrice: String,
    val rechargeDiscountNum: String,
    val anchorPriceLeveD: String,
    val privatePhotoRate: String,
    val faceLeaveTime: String,
    val giftReward: String,
    val extractRate: String,
    val randomMatchRate: String,
    val anchorPrivatePhoto: String,
    val lottery_100: String,
    val faceUploadTime: String,
    val giftRate: String,
    val lottery_20: String,
    val lottery_1: String,
    val videoCallRate: String,
    val sport_3: String,
    val rechargeReward: String,
    val anchorDefaultCallAmount: String,
    val freeTimeRate: String,
    val anchorPriceLevelB: String,
    val sport_2: String,
    val anchorPriceLevelC: String,
    val sport_1: String,
    val anchorPriceLevelE: String,
    val vipVideoCallSpecialOffer: String,
    val vipMatchSpecialOffer: String,
    val showWhatsAppCoin: Int
) : Parcelable
