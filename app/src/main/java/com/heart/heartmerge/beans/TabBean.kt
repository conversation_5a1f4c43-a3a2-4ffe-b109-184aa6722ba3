package com.heart.heartmerge.beans

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/20 15:22
 * @description :
 */
@JsonClass(generateAdapter = true)
@Parcelize
data class TabBean(
    val content: String = "",
    val type: String = "",
    val lock: Boolean = false,
    val open_conditions: List<String>? = null,
    val jump_url: String = "",
    val strategy_type: Int = 0
) : Parcelable {
    val isWholePage get() =  strategy_type == 0

    val isPartPage get() =  strategy_type == 1
}
