package com.heart.heartmerge.mmkv

import com.appsflyer.AppsFlyerLib
import com.bdc.android.library.mmkv.MMKVOwner
import com.bdc.android.library.mmkv.mmKvBool
import com.bdc.android.library.mmkv.mmKvString
import com.google.firebase.analytics.FirebaseAnalytics
import com.heart.heartmerge.utils.AdvertisingIdClient
import com.heart.heartmerge.utils.ContextHolder
import com.tencent.mmkv.MMKV

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 5:56 下午
 * @description：mmKv base 数据存储库
 */
object MMKVGuiYinDataRep : MMKVOwner {
    override val kv: MMKV = MMKV.mmkvWithID("app_guiyin")
    var googleInstallReferrer by mmKvString()
    var appsFlyerAppId by mmKvString()
    var appsFlyerInstallReferrer by mmKvString()
    var firebaseAppId by mmKvString()

    var googleAdvertisingId by mmKvString()

    var isOrganicUser by mmKvBool(true)

    fun clearAll() {
    }

    fun fetchAppsflyerAppId() = runCatching {
        appsFlyerAppId ?: AppsFlyerLib.getInstance().getAppsFlyerUID(ContextHolder.context)
            ?.takeIf { it.isNotEmpty() }
    }.getOrNull()?.also { appsFlyerAppId = it } ?: ""

    fun fetchFirebaseAppId() = runCatching {
        firebaseAppId
            ?: FirebaseAnalytics.getInstance(ContextHolder.context).firebaseInstanceId.takeIf { it.isNotEmpty() }
    }.getOrNull()?.also { firebaseAppId = it } ?: ""

    fun fetchGoogleAdvertisingId() = runCatching {
        googleAdvertisingId ?: AdvertisingIdClient.getGoogleAdId(ContextHolder.context)
            .takeIf { it.isNotEmpty() }
    }.getOrNull()?.also { googleAdvertisingId = it } ?: ""
}