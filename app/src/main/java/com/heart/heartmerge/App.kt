package com.heart.heartmerge

import ai.datatower.analytics.DT
import ai.datatower.analytics.DTAnalytics
import ai.datatower.analytics.DTChannel
import android.app.Application
import android.content.Context
import android.os.Build
import android.util.Log
import android.webkit.WebView
import androidx.core.content.ContextCompat
import com.bdc.android.library.ktnet.netWorkFailedInterceptor
import com.bdc.android.library.provider.ContextWrapperProvider
import com.bdc.android.library.utils.Logger
import com.facebook.FacebookSdk
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.i18n.I18nContextWrapper
import com.heart.heartmerge.i18n.I18nManager
import com.heart.heartmerge.ktnet.interception.MyNetFailedInterceptor
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.FileResourceManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.ui.widget.floatwindow.BaseActivityLifecycleCallbacks
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.GoogleAdvertisingIdHelper
import com.heart.heartmerge.utils.InstallChannelUtil
import com.lxj.xpopup.XPopup
import com.tencent.mmkv.MMKV


class App : Application() {

    private var mLifecycleCallbacks: BaseActivityLifecycleCallbacks? = null

    override fun onCreate() {
        super.onCreate()
        ContextHolder.init(this)
        setupCrashHandler()

        val processName = AppUtil.getProcessName()
        if (packageName == processName) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                runCatching {
                    WebView.setDataDirectorySuffix("$processName.webview")
                }
            }

            initializeThirdSdk()

            LogX.i("Install Channel : ${InstallChannelUtil.getInstallSource(this)}")

            // 重试上传之前失败的日志文件
//            UploadQueueManager.retryAllPending()
            FileResourceManager.initialize()

            // 基础初始化I18n管理器（不加载翻译数据）
            I18nManager.init(this)

            ContextWrapperProvider.wrap = { base ->
                I18nContextWrapper(base)
            }
            I18nManager.updateLanguageDataAsync(this)
        }
    }

    private fun setupCrashHandler() {
        val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            LogX.e("CrashHandler", "应用崩溃！线程: ${thread.name}, 错误信息: ${throwable.message}")
            LogX.e("CrashHandler", throwable) // 打印完整的堆栈信息到日志文件
            // 强制上传日志，确保崩溃信息被记录
            LogX.forceUpload()

            // 调用系统默认的异常处理器，确保应用崩溃行为符合预期
            defaultUncaughtExceptionHandler?.uncaughtException(thread, throwable)
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        // 在应用程序终止时释放 WebSocket 资源
        WebSocketManager.getInstance().release()
    }

    private fun initializeThirdSdk() {
        FacebookSdk.setAutoInitEnabled(false)
        initGoogleAdvertisingId()

        try {
            MMKV.initialize(this)
        } catch (e: UnsatisfiedLinkError) {
            e.printStackTrace()
            ReportManager.logException(e)
        }

        MMKVBaseDataRep.rongCloudAppKey?.let {
            AppUtil.initRongYunIM(it)
        }

        registerActivityLifecycleCallbacks()

        FirebaseApp.initializeApp(this)

        MMKVBaseDataRep.facebookId?.let {
            AppUtil.initFacebookSdk(it, MMKVBaseDataRep.facebookClientToken ?: "")
        }

        DT.initSDK(
            this,
            "dt_e4855a3a63bff6ec",
            "https://report.roiquery.com",
            DTChannel.GP,
            BuildConfig.DEBUG,
            Log.DEBUG,
            false
        )

        FirebaseAnalytics.getInstance(this).appInstanceId.addOnSuccessListener {
            LogX.i("FirebaseAnalytics firebaseAppId $it")
            DTAnalytics.setFirebaseAppInstanceId(it)
            MMKVGuiYinDataRep.firebaseAppId = it
        }

        XPopup.setNavigationBarColor(ContextCompat.getColor(this, R.color.background))

        netWorkFailedInterceptor = MyNetFailedInterceptor()

        initGoogleAdvertisingId()
    }

    /**
     * 初始化获取Google广告ID
     */
    private fun initGoogleAdvertisingId() {
        GoogleAdvertisingIdHelper.getAdvertisingId(
            this, object : GoogleAdvertisingIdHelper.AdvertisingIdCallback {
                override fun onAdvertisingIdObtained(advertisingId: String?) {
                    LogX.d("Google Advertising ID: $advertisingId")
                    MMKVGuiYinDataRep.googleAdvertisingId = advertisingId
                }

                override fun onFailure(e: Exception) {
                    LogX.e("Failed to get Google Advertising ID: " + e.message)
                }
            })
    }

    private fun registerActivityLifecycleCallbacks() {
        mLifecycleCallbacks = BaseActivityLifecycleCallbacks()
        registerActivityLifecycleCallbacks(mLifecycleCallbacks)
    }

    fun getLifecycleCallbacks(): BaseActivityLifecycleCallbacks? = mLifecycleCallbacks

    override fun attachBaseContext(base: Context) {
        Logger.i("App", "🔧 Application attachBaseContext with basic I18n setup")

        // 暂时禁用全局 ContextWrapper，避免与第三方库冲突
        // 改为在具体的 Activity 中按需应用
//        super.attachBaseContext(I18nContextWrapper(base))
        super.attachBaseContext(base)
    }
}