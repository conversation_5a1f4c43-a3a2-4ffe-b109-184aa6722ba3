package com.heart.heartmerge.popup

import android.app.Activity
import android.text.SpannableString
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.view.Gravity
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.PopupPlatformRuleBinding
import com.heart.heartmerge.manager.MainPopupPriority
import com.heart.heartmerge.manager.PriorityDialogManager
import com.heart.heartmerge.mmkv.MMKVDataRep
import java.lang.ref.WeakReference

class PlatformRulePopup(activity: Activity) : BasePriorityPopup(activity) {

    private val weakActivity = WeakReference(activity)

    override fun getImplLayoutId(): Int = R.layout.popup_platform_rule

    override fun initPopupContent() {
        super.initPopupContent()
        val binding = PopupPlatformRuleBinding.bind(contentView)

        val symbol = "•"
        val contentOne = weakActivity.get()?.getString(R.string.main_platform_rule_content_one)
        val spannableText = SpannableString("$symbol $contentOne")
        spannableText.setSpan(
            AbsoluteSizeSpan(30, true), 0, symbol.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.tvContentOne.text = spannableText
        binding.tvContentOne.gravity = Gravity.CENTER_VERTICAL

        val contentTwo = weakActivity.get()?.getString(R.string.main_platform_rule_content_two)
        val spannableTextTwo = SpannableString("$symbol $contentTwo")
        spannableTextTwo.setSpan(
            AbsoluteSizeSpan(30, true), 0, symbol.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.tvContentTwo.text = spannableTextTwo
        binding.tvContentTwo.gravity = Gravity.CENTER_VERTICAL

        val contentThree = weakActivity.get()?.getString(R.string.main_platform_rule_content_three)
        val spannableTextThree = SpannableString("$symbol $contentThree")
        spannableTextThree.setSpan(
            AbsoluteSizeSpan(30, true), 0, symbol.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.tvContentThree.text = spannableTextThree
        binding.tvContentThree.gravity = Gravity.CENTER_VERTICAL

        binding.btnKnow.setOnClickListener {
            MMKVDataRep.isPlatformRuleRead = true //缓存
            PriorityDialogManager.removeData(MainPopupPriority.FIRST_LAUNCHER_PLATFORM_RULE) //删除队列
            dismiss()
        }
    }
}