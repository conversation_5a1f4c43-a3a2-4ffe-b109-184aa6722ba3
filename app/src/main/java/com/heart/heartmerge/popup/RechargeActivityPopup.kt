package com.heart.heartmerge.popup

import android.annotation.SuppressLint
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.observeOnce
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.databinding.PopupRechargeActivityBinding
import com.heart.heartmerge.extension.applyGooglePrice
import com.heart.heartmerge.extension.applyRechargeCountdown
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.FlexibleCountdownTimer
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.UserViewModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.lxj.xpopup.util.XPopupUtils
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/9/6 9:44
 * @description :充值活动弹框
 */
class RechargeActivityPopup(activity: AppCompatActivity) : CenterPopupView(activity) {

    private val weakActivity = WeakReference(activity)
    private val userViewModel by activity.viewModels<UserViewModel>()
    private lateinit var binding: PopupRechargeActivityBinding
    private var billingService: BillingService? = null
    private var flexibleCountdownTimer: FlexibleCountdownTimer? = null

    override fun getImplLayoutId(): Int = R.layout.popup_recharge_activity

    override fun getMaxWidth(): Int {
        return XPopupUtils.getAppWidth(context) * 0.9f.toInt()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun initPopupContent() {
        super.initPopupContent()
        billingService = weakActivity.get()?.let { BillingService(it) }
        binding = PopupRechargeActivityBinding.bind(popupImplView)
        binding.ivClose.click { dismiss() }
        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        binding.tvTip.click {
            WebViewActivity.jump(
                activity,
                activity.getString(R.string.recharge_agreement),
                Constants.Agreement.RECHARGE_URL
            )
        }
        fetchData()
    }

    private fun fetchData() {
        if (weakActivity.get() == null || weakActivity.get()?.isFinishing == true) return
        userViewModel.fetchGoodsList(
            goodsType = GoodsQueryType.ALL
        ).asLiveData().observeOnce(weakActivity.get()) { result ->
            val firstRechargeList = result?.goldGoods?.filter { it.hasCountdown }
            val list = buildList {
                val current =
                    firstRechargeList?.filter { it.hasCountdown }?.minByOrNull { it.deadline }
                current?.let { add(it) }
                result?.goldGoods?.let {
                    addAll(it.filter { !it.hasCountdown })
                }
            }

            binding.recyclerView.clearAllItems()

            lifecycleScope.launch {
                billingService?.let {
                    firstRechargeList?.applyGooglePrice(it)
                    list.applyGooglePrice(it)
                }
                binding.recyclerView.append<DslAdapterItem>(list) {
                    itemLayoutId = R.layout.item_recharge_activity
                    itemBindOverride = { itemHolder, itemPosition, _, _ ->
                        val item = itemData as GoodsBean
                        if (item.isSubscribe) {
//                            itemHolder.tv(R.id.tv_title)?.text =
//                                item.sku.toSubscriptionTitle()
                            itemHolder.tv(R.id.tv_title)?.text = "${item.title}"
                            itemHolder.img(R.id.iv_icon)?.setBackgroundResource(
                                    when (item.sku) {
                                        "subweek" -> R.mipmap.ic_label_bronze_vip
                                        "submonth" -> R.mipmap.ic_label_silver_vip
                                        "sub3month" -> R.mipmap.ic_label_gold_vip
                                        else -> R.mipmap.ic_vip_crown
                                    }
                                )
                        } else {
                            itemHolder.tv(R.id.tv_title)?.text =
                                item.coin.toInt().toShowDiamond().toString()
                            itemHolder.img(R.id.iv_icon)?.apply {
                                val resId =
                                    AppUtil.getDrawableByName("ic_diamond_recharge_level${if (itemPosition < 6) itemPosition + 1 else 6}")
                                resId?.let {
                                    setBackgroundResource(resId)
                                }
                                    ?: run { setBackgroundResource(R.mipmap.ic_diamond_recharge_level6) }
                            }
                        }

                        itemHolder.tv(R.id.tv_label)?.apply {
                            makeVisible(item.hasDiscount || item.hasCountdown)
                            text = if (item.hasCountdown) {
                                itemHolder.v<View>(R.id.container)?.setBackgroundResource(
                                    R.drawable.shape_white_radius12
                                )
                                item.countdown
                            } else {
                                itemHolder.v<View>(R.id.container)?.setBackgroundResource(
                                    R.drawable.shape_white50_radius12
                                )
                                "${(item.discount * 100)}% OFF"
                            }
                        }
                        itemHolder.view(R.id.ll_give_container)
                            ?.makeVisible(item.level_bonus.isDigitsOnly() && item.level_bonus.toInt() > 0)
                        itemHolder.tv(R.id.tv_level)?.text = itemHolder.context.getString(
                            if (item?.isSubscribe == true) R.string.recharge_level_bonus_vip else R.string.recharge_level_bonus,
                            result?.level_config?.level
                        )
                        itemHolder.tv(R.id.tv_give_diamond)?.text =
                            item.level_bonus.isDigitsOnly().let {
                                item.level_bonus.toInt().toShowDiamond().toString()
                            }
                        itemHolder.v<TextView>(R.id.btn_recharge)?.apply {
                            text =
                                item.googleExtras?.subscriptionOfferDetails?.firstOrNull()?.pricingPhases?.pricingPhaseList?.firstOrNull()?.formattedPrice
                                    ?: item.googleExtras?.oneTimePurchaseOfferDetails?.formattedPrice
                                            ?: item.formattedPrice

                            if (item.isSubscribe) {
                                background = context.getDrawable(R.drawable.selector_gold_button)
                                setTextCompatColor(R.color.color_FF962D)
                            } else {
                                background = context.getDrawable(R.drawable.shape_border_9656ff)
                                setTextCompatColor(R.color.color_9656FF)
                            }
                        }

                        itemHolder.clickItem {
                            weakActivity.get()?.apply {
                                showChoosePaymentPopup(
                                    this,
                                    item,
                                    purchaseScene = if (firstRechargeList?.firstOrNull { it.id == item.id } != null) PurchaseScene.FirstRecharge else PurchaseScene.RechargePromotion) {
                                    if (it) {
                                        dismiss()
                                    }
                                }
                            }
                        }
                    }
                }

                binding.progressBar.makeGone()

                if (firstRechargeList?.isNotEmpty() == true) {
                    flexibleCountdownTimer?.cancel()
                    flexibleCountdownTimer =
                        binding.recyclerView.applyRechargeCountdown(firstRechargeList)
                }
            }
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        flexibleCountdownTimer?.cancel()
        billingService?.onDestroy()
        weakActivity.clear()
    }
}

fun showRechargeActivityPopup(activity: AppCompatActivity) {
    XPopup.Builder(activity).dismissOnBackPressed(false).dismissOnTouchOutside(false)
        .asCustom(RechargeActivityPopup(activity)).show()
}