package com.heart.heartmerge.viewmodes

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.mvi.setState
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.RateLabelBean
import com.heart.heartmerge.beans.RateLabelList
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.repo.service.anchor.AnchorParams
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.RongMessageUtil
import io.rong.imkit.IMCenter
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.UserInfo
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlin.math.roundToLong


class AnchorViewModel : BaseViewModel() {
    private val service: AnchorService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<SearchRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    fun fetchAnchorDetail(anchorId: String) {
        viewModelScope.launch {
            val anchor = service.getAnchorDetail(anchorId).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.GetAnchorDetailFailed(it.msg))
            }
            anchor?.let {
                //缓存融云信息
                RongMessageUtil.refreshCacheUserInfo(it)
                _pageEvents.setEvent(SearchRequestEvent.GetAnchorDetailSuccess(it))
            }
        }
    }

    fun fetchAnchorBottomRecommendation() {
        viewModelScope.launch {
            val anchorList = service.getHomepage(8, "", 20).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.GetAnchorBottomRecommendationFailed(it.msg))
            }
            anchorList?.let {list ->
//                list.records?.takeIf { it.size >= 6 }?.subList(0, 6)
                _pageEvents.setEvent(SearchRequestEvent.GetAnchorBottomRecommendationSuccess(list.records))
            }
        }
    }

    fun searchAnchor(current: Int, size: Int, countId: String = "", keyword: String) {
        viewModelScope.launch {
            val anchors = service.searchAnchor(
                AnchorParams.searchAnchorParamsBody(
                    current, size, countId, keyword
                )
            ).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.AnchorSearchFailed(it.msg))
            }
            anchors?.let {
                _pageEvents.setEvent(SearchRequestEvent.AnchorSearchSuccess(it.records, it.countId))
            }
        }
    }

    fun anchorFollow(anchorId: String) {
        viewModelScope.launch {
            val response = service.follow(AnchorParams.relationOpParamsBody(anchorId)).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.AnchorFollowFailed(it.msg))
            }
            response?.let {}
        }
    }

    fun anchorUnfollow(anchorId: String) {
        viewModelScope.launch {
            val response = service.unFollow(AnchorParams.relationOpParamsBody(anchorId)).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.AnchorUnFollowFailed(it.msg))
            }
            response?.let {}
        }
    }

    fun blackAnchor(anchorId: String) {
        viewModelScope.launch {
            val result = service.black(AnchorParams.relationOpParamsBody(anchorId)).tryAwait {
                ToastUtil.show(ContextHolder.context.getString(R.string.tip_black_failed))
            }
            result?.let {
                ToastUtil.show(ContextHolder.context.getString(R.string.tip_black_suc))
                _pageEvents.setEvent(SearchRequestEvent.AnchorBlackSuccess)
                FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).post(anchorId)
                IMCenter.getInstance().removeConversation(
                    Conversation.ConversationType.PRIVATE,
                    anchorId,
                    object : RongIMClient.ResultCallback<Boolean>() {
                        override fun onSuccess(success: Boolean) {
                            LogX.e("block anchor remove conversation success")
                        }

                        override fun onError(errorCode: RongIMClient.ErrorCode) {
                            LogX.e("block anchor remove conversation failed,code: ${errorCode.code}, msg:${errorCode.msg}")
                        }
                    })
            }
        }
    }

    fun getRateLabels() {
        viewModelScope.launch {
            val request = service.getRateLabels().tryAwait { }
            request?.let {
                _pageEvents.setEvent(SearchRequestEvent.GetRateLabelsSuccess(it))
            }
        }
    }

    fun fetchAnchorList(anchorIds: MutableList<String>) {
        viewModelScope.launch {
            val anchors = service.getAnchorList(AnchorParams.getAnchorListBody(anchorIds)).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.FetchAnchorListFailed(anchorIds, it.msg))
            }
            anchors?.list?.forEach {
                RongMessageUtil.refreshCacheUserInfo(it)
            }
        }
    }

    fun anchorRate(
        score: Int,
        callId: String,
        tagIds: MutableList<Int>,
    ) {
        viewModelScope.launch {
            val request = service.anchorRate(
                AnchorParams.anchorRateParamBody(
                    score, callId, tagIds
                )
            ).tryAwait {
                _pageEvents.setEvent(SearchRequestEvent.AnchorRateFailed(it.msg))
            }
            request?.let {
                _pageEvents.setEvent(SearchRequestEvent.AnchorRateSuccess)
            }
        }
    }

    fun userAlbumPay(msgId: String) {
        viewModelScope.launch {
            val anchor = service.userAlbumPay(AnchorParams.albumBuyParamBody(msgId)).tryAwait {
//                _pageEvents.setEvent(SearchRequestEvent.UserAlbumPayFailed(it.msg))
                _pageEvents.setEvent(SearchRequestEvent.UserAlbumPaySuccess)
            }
            anchor?.let {
                _pageEvents.setEvent(SearchRequestEvent.UserAlbumPaySuccess)
            }
        }
    }
}


sealed class SearchRequestEvent {
    data class AnchorSearchSuccess(val list: MutableList<UserBean>?, val countId: String) :
        SearchRequestEvent()

    data class AnchorSearchFailed(val msg: String) : SearchRequestEvent()
    data class AnchorFollowFailed(val msg: String) : SearchRequestEvent()
    data class AnchorUnFollowFailed(val msg: String) : SearchRequestEvent()
    data class GetAnchorDetailSuccess(val userBean: UserBean?) : SearchRequestEvent()
    data class GetAnchorBottomRecommendationSuccess(val list: MutableList<UserBean>?) : SearchRequestEvent()
    data class GetAnchorBottomRecommendationFailed(val msg: String) : SearchRequestEvent()
    data class GetAnchorDetailFailed(val msg: String) : SearchRequestEvent()
    data class UserAlbumPayFailed(val msg: String) : SearchRequestEvent()
    data object UserAlbumPaySuccess : SearchRequestEvent()
    data object AnchorBlackSuccess : SearchRequestEvent()
    data class GetRateLabelsSuccess(val rateLabelBeans: MutableList<RateLabelList>) :
        SearchRequestEvent()

    data class AnchorRateFailed(val msg: String) : SearchRequestEvent()
    data object AnchorRateSuccess : SearchRequestEvent()

    data class VideoReportSuccess(val requestId: Long) : SearchRequestEvent()
    data class FetchAnchorListFailed(val anchorIds: MutableList<String>, val msg: String) : SearchRequestEvent()
}
