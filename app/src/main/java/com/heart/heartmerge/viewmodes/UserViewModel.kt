package com.heart.heartmerge.viewmodes

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.http.request
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.utils.ToastUtil
import com.google.gson.JsonObject
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.AuthBean
import com.heart.heartmerge.beans.BackpackBean
import com.heart.heartmerge.beans.DefaultAvatarBean
import com.heart.heartmerge.beans.DiamondFlowBean
import com.heart.heartmerge.beans.GoodsQueryType
import com.heart.heartmerge.beans.GoodsWrapperBean
import com.heart.heartmerge.beans.PageBean
import com.heart.heartmerge.beans.RelationAction
import com.heart.heartmerge.beans.RelationList
import com.heart.heartmerge.beans.SignInBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.UserSource
import com.heart.heartmerge.beans.VersionBean
import com.heart.heartmerge.beans.WalletTransactionBean
import com.heart.heartmerge.extension.requestChannelFlow
import com.heart.heartmerge.firebase.report.DT_EVENT_CONSTANTS
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.manager.DoNotDisturbManager
import com.heart.heartmerge.manager.FileUploadManager
import com.heart.heartmerge.manager.onError
import com.heart.heartmerge.manager.onSuccess
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.mmkv.MMKVGuiYinDataRep
import com.heart.heartmerge.repo.service.user.UserParams
import com.heart.heartmerge.repo.service.user.UserService
import com.heart.heartmerge.ui.activities.login.LOGIN_TYPE
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.AppsFlyerHelper
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.InstallChannelUtil
import com.heart.heartmerge.utils.RongMessageUtil
import com.heart.heartmerge.utils.UniqueIDUtil
import com.heart.heartmerge.utils.toJson
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import java.io.File


class UserViewModel : BaseViewModel() {
    private val service: UserService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<SettingRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()
    val mSendMsgNumState: MutableLiveData<Int> = MutableLiveData<Int>()
    private val refreshTrigger = MutableSharedFlow<Unit>(replay = 1)

    private val _transactionCursor = MutableLiveData<String>("")
    private val _rechargeCursor = MutableLiveData<String>("")
    private val _blackCursor = MutableLiveData<String>("")

    fun reportGoogleReferrer(referrer: String): Flow<UserSource> = requestChannelFlow {
        request = {
            aggregatedService.reportReferer(buildMap {
                put("google_referer", referrer)
                put("google_gaid", MMKVGuiYinDataRep.fetchGoogleAdvertisingId())
                put("firebase_app_id", MMKVGuiYinDataRep.fetchFirebaseAppId())
                put("appsflyer_id", MMKVGuiYinDataRep.fetchAppsflyerAppId())
                put("appsflyer_id", MMKVGuiYinDataRep.fetchAppsflyerAppId())
                put(
                    "install_channel",
                    InstallChannelUtil.getInstallSource(ContextHolder.context).toJson()
                )
            })
        }
    }

    fun login(
        type: LOGIN_TYPE, authId: String, failureBlock: () -> Unit
    ): Flow<UserBean> = requestChannelFlow<AuthBean, UserBean> {
        val params = buildMap {
            put("auth_type", type.value.toString())
            put("auth_id", authId)
            put("auth_token", authId)
            put("deviceId", UniqueIDUtil.getUniqueID(ContextHolder.context))
//            put("platform", "android")
//            put("userCategory", MMKVGuiYinDataRep.googleInstallReferrer ?: "")
//            put("appsFlyerAppId", MMKVGuiYinDataRep.fetchAppsflyerAppId())
//            put(
//                "afReferrer", URLEncoder.encode(
//                    MMKVGuiYinDataRep.appsFlyerInstallReferrer ?: "", "UTF-8"
//                )
//            )
//            put("firebaseAppId", MMKVGuiYinDataRep.fetchFirebaseAppId())

        }.toMutableMap()
        request = {
            ReportManager.logEvent(
                DT_EVENT_CONSTANTS.EVENT_PREPARE_LOGIN, params
            )
            aggregatedService.login(params)
        }
        transform = {
            params.put("userId", it.user.id)
            params.put("username", it.user.username)
            params.put("nickName", it.user.nickname)
            ReportManager.setUserProperty(it.user.nickname, it.user.id)
            if (it.user.isRegistration) {
                ReportManager.logRegistration(params = params)
            } else {
                ReportManager.logLogin(params = params)
            }
            it.apply {
                MMKVBaseDataRep.token = it.accessToken
                MMKVBaseDataRep.agoraRtcAppId = it.config.agoraConfig.appID
                MMKVBaseDataRep.rongCloudAppKey = it.user.rongcloudAppID
                MMKVBaseDataRep.rongYunToken = it.user.rongcloudToken
                MMKVBaseDataRep.facebookId = "facebookId"
                MMKVBaseDataRep.facebookClientToken = "facebookClientToken"
                MMKVDataRep.userInfo = it.user
                refreshUser()
                AppUtil.initRongYunIM(it.user.rongcloudAppID)
                AppUtil.initFacebookSdk("facebookId", "facebookClientToken")
            }

            if (BuildConfig.DEBUG) {
                AppsFlyerHelper.initialize()
            }
            it.user
        }
        failure = {
            failureBlock.invoke()
            ReportManager.logException(it)
        }
    }

    fun logout(userId: String): Flow<Any> = requestChannelFlow {
        request = { aggregatedService.logout(userId) }
    }

    fun bindGoogle(authId: String, authToken: String) = requestChannelFlow {
        request = {
            aggregatedService.googleBind(buildMap {
                put("auth_id", authId)
                put("auth_token", authToken)
            })
        }
    }

    fun getDefaultAvatar(): Flow<DefaultAvatarBean> = requestChannelFlow {
        request = { aggregatedService.defaultAvatarList() }
    }

    fun updateProfile(params: JsonObject): Flow<AuthBean> = requestChannelFlow {
        request = {
            aggregatedService.updateProfile(params.apply {
//                addProperty("id", MMKVDataRep.userInfo.id)
                addProperty("gender", 1)
            })
        }
        transform = {
            MMKVBaseDataRep.token = it.accessToken
            it
        }
    }

    fun refreshUser() {
        refreshTrigger.tryEmit(Unit)
        request {
            call { aggregatedService.getUserDetail() }
            onSuccess {
                it?.let { authBean ->
                    MMKVDataRep.userConfig = authBean.config
                    MMKVBaseDataRep.token = authBean.accessToken
                    MMKVDataRep.userInfo = authBean.user
                    RongMessageUtil.refreshCacheUserInfo(authBean.user)
                }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val userBean: Flow<UserBean> = flow {
        emit(MMKVDataRep.userInfo) // 先发射缓存的数据
        refreshTrigger.flatMapLatest {
            requestChannelFlow<AuthBean, UserBean> {
                request = {
                    aggregatedService.getUserDetail()
                }
                transform = {
                    MMKVDataRep.userConfig = it.config
                    MMKVDataRep.userInfo = it.user
                    MMKVBaseDataRep.token = it.accessToken
                    it.user
                }
            }
        }.collect {
            if (it is UserBean) {
                emit(it)
            }
        }
    }

    fun upload(path: String): Flow<FileUploadManager.FileUploadResult> = flow {
        FileUploadManager.upload(
            File(path), FileUploadManager.UploadConfig(fileType = FileUploadManager.FileType.IMAGE)
        ).onSuccess { result ->
            emit(result)
        }.onError { error ->
            emit(FileUploadManager.FileUploadResult(false, errorMessage = error))
            ToastUtil.show(error)
        }
    }

    fun fetchGoodsList(
        goodsType: GoodsQueryType = GoodsQueryType.DIAMOND,/*1-只查询钻石列表，2-会员订阅列表，3-首充*/
        countryId: String = ""
    ): Flow<GoodsWrapperBean?> = requestChannelFlow {
        request = {
            aggregatedService.getGoodsList(
                goodsType.value,
                countryId.takeIf { it.isNotEmpty() }
                    ?: MMKVDataRep.userInfo.userCountry?.id.toString())
        }
    }

    fun fetchMatchingCardCount(): Flow<Int> = requestChannelFlow {
        request = { aggregatedService.getMatchingCardCount() }
    }

    fun fetchMatchingRecordList(
        increasing: Int/*1-获取，0-消耗*/, pageIndex: Int
    ): Flow<List<DiamondFlowBean>> =
        requestChannelFlow<PageBean<DiamondFlowBean>, List<DiamondFlowBean>> {
            request = {
                aggregatedService.getMatchingCardList(buildMap {
                    put("current", pageIndex.toString())
                    put("size", "20")
                    put("increasing", increasing.toString())
                })
            }
            transform = { it.records }
        }

    fun fetchWalletTransactionList(
        refresh: Boolean = false
    ): Flow<List<WalletTransactionBean>?> =
        requestChannelFlow<PageBean<WalletTransactionBean>, List<WalletTransactionBean>> {
            request = {
                if (refresh) {
                    _transactionCursor.value = ""
                }
                aggregatedService.getWalletTransactionList(_transactionCursor.value.toString(), 20)
            }
            transform = {
                _transactionCursor.value = it.cursor
                it.records
            }
        }

    fun fetchSignInList(): Flow<SignInBean> = requestChannelFlow {
        request = { aggregatedService.getSignInList() }
    }

    fun signIn(recordId: String): Flow<Any> = requestChannelFlow {
        request = {
            aggregatedService.signIn(buildMap {
                put("record_id", recordId.toInt())
            })
        }
    }

    fun fetchBackpackList(): Flow<List<BackpackBean>> = requestChannelFlow {
        request = { aggregatedService.getBackpackList() }
    }

    fun fetchBlackList(refresh: Boolean = false): Flow<List<UserBean>> =
        requestChannelFlow<PageBean<UserBean>, List<UserBean>> {
            request = {
                if (refresh) {
                    _blackCursor.value = ""
                }
                aggregatedService.getBlacklistList(
                    RelationList.BLOCK.value, _blackCursor.value.toString()
                )
            }
            transform = {
                _blackCursor.value = it.cursor
                it.records
            }
        }

    fun cancelAccount(): Flow<Any> = requestChannelFlow {
        request = {
            aggregatedService.cancelAccount()
        }
    }

    fun updateDoNotDisturb(isOpen: Boolean) {
        viewModelScope.launch {
            val response =
                service.updateDisturbStatus(UserParams.updateDisturbParamsBody(isOpen)).tryAwait {
                    _pageEvents.setEvent(SettingRequestEvent.UpdateDoNotDisturbFailed(!isOpen))
                }
            response?.let {
                MMKVDataRep.userInfo.dnd_expire = it.dnd_expire
                DoNotDisturbManager.switchDoDotDisturbMode(isOpen)
            }
        }
    }

    fun getReportList() = requestChannelFlow {
        request = { aggregatedService.getReportList() }
    }

    fun anchorReport(reportUserId: Int, reportContent: String, reportLang: String = "en") {
        viewModelScope.launch {
            val response = service.userReport(
                UserParams.userReportParamsBody(
                    reportUserId, reportContent, reportLang
                )
            ).tryAwait {
                _pageEvents.setEvent(SettingRequestEvent.AnchorReportFailed(it.msg))
            }
            response?.let {
                _pageEvents.setEvent(SettingRequestEvent.AnchorReportSuccess)
            }
        }

    }

    fun subtractMsgSend() {
        viewModelScope.launch {
            val response = service.subtractMsgSend().tryAwait {
                ToastUtil.show(ContextHolder.context.getString(R.string.tip_send_failed))
            }
            response?.let {
                mSendMsgNumState.postValue(it)
            }
        }

    }

    fun getWhoSeeMe() {
        viewModelScope.launch {
            val response = service.getWhoSeeMe().tryAwait {
                ToastUtil.show(it.msg)
            }
            response?.let {
                _pageEvents.setEvent(
                    SettingRequestEvent.GetWhoSeeMeSuccess(
                        it.records ?: emptyList()
                    )
                )
            }
        }

    }

    fun removeBlack(userId: String): Flow<Any?> = requestChannelFlow {
        request = {
            aggregatedService.removeBlack(RelationAction.UNBLOCK.value, buildMap {
                put("peerId", userId.toInt())
            })
        }
    }

    fun checkVersion() = requestChannelFlow<VersionBean> {
        request = { aggregatedService.checkVersion() }
    }

    fun getLevelConfig() = requestChannelFlow {
        request = { aggregatedService.getLevelConfig() }
    }

    fun getLevelTaskList() = requestChannelFlow {
        request = { aggregatedService.getTaskList() }
    }

    fun getRechargeList(refresh: Boolean = false, type: GoodsQueryType/*1充值记录 2订阅记录*/) =
        requestChannelFlow {
            request = {
                if (refresh) {
                    _rechargeCursor.value = ""
                }
                aggregatedService.getRechargeList(
                    productType = type.value, _rechargeCursor.value.toString(), 20
                )
            }
            transform = {
                _rechargeCursor.value = it.cursor
                it
            }
        }

    fun taskClaim(id: Int) = requestChannelFlow {
        request = {
            aggregatedService.taskClaim(buildMap {
                put("task_id", id)
            })
        }
    }

    fun getSubscribeBenefit(priceId: String) = requestChannelFlow {
        request = { aggregatedService.getSubscribeBenefit(priceId) }
    }

    fun bonusClaim(recordId: Int) = requestChannelFlow {
        request = { aggregatedService.bonusClaim(mapOf("record_id" to recordId)) }
    }

    fun getFreeRandomMatchCredential() = requestChannelFlow {
        request = { aggregatedService.getFreeRandomMatchCredential() }
    }
}

sealed class SettingRequestEvent {
    data class UpdateDoNotDisturbFailed(val orgStatus: Boolean) : SettingRequestEvent()
    data class AnchorReportFailed(val msg: String) : SettingRequestEvent()
    data object AnchorReportSuccess : SettingRequestEvent()
    data class GetWhoSeeMeSuccess(val users: List<UserBean>) : SettingRequestEvent()
    data object AnchorReportDefault : SettingRequestEvent()
}