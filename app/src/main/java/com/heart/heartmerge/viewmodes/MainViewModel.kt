package com.heart.heartmerge.viewmodes

import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.heart.heartmerge.beans.ChatTipBean
import com.heart.heartmerge.beans.RemoteCallGoldChangeExtend
import com.heart.heartmerge.beans.RemoteCallRefuseExtend
import com.heart.heartmerge.beans.RemoteNotifyWindowChangeExtend
import com.heart.heartmerge.beans.RemoteSocketStatusChangeExtend
import com.heart.heartmerge.beans.RemoteUserCleanMatchExtend
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.WebSocketMessage
import com.heart.heartmerge.beans.WebSocketResponse
import com.heart.heartmerge.extension.toMD5
import com.heart.heartmerge.ktnet.NetDelegates
import com.heart.heartmerge.ktnet.error.msg
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DownloadVideoManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.repo.service.anchor.AnchorParams
import com.heart.heartmerge.repo.service.anchor.AnchorService
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.ui.activities.anchor.AnchorVideoActivity
import com.heart.heartmerge.ui.activities.anchor.IncomingVideoActivity
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.fromJson
import com.heart.heartmerge.utils.toJson
import io.rong.imkit.IMCenter
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatSystemMessage
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageBlockType
import io.rong.imlib.translation.TranslationClient
import io.rong.imlib.translation.TranslationResultListener
import io.rong.message.CommandMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.io.File
import java.lang.ref.WeakReference
import java.util.Collections


class MainViewModel : BaseViewModel() {
    // 用于存储已处理消息的 seq，防止重复处理，并限制最大数量
    private val processedMessageSeqs: MutableMap<String, Long> = Collections.synchronizedMap(object : LinkedHashMap<String, Long>(500, 0.75f, true) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, Long>?): Boolean {
            return size > 500 // 当容量超过500时，移除最老的条目
        }
    })

    // 静态内部类实现消息块回调，避免内存泄漏
    private class MessageBlockCallback : IRongCoreCallback.ResultCallback<Message?>() {
        override fun onSuccess(t: Message?) {
            t?.let {
                val expansion = hashMapOf<String, String>(Constants.MESSAGE_EXPEND_KEY_BLOCK to "1")
                RongIMClient.getInstance().updateMessageExpansion(expansion, it.uId, MessageExpansionCallback(it))
            }
        }

        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
            // 错误处理
        }
    }

    // 静态内部类实现消息扩展回调，避免内存泄漏
    private class MessageExpansionCallback(private val message: Message) : RongIMClient.OperationCallback() {
        override fun onSuccess() {
            LogX.e("updateMessageExpansion success")
            val expansion = hashMapOf<String, String>(Constants.MESSAGE_EXPEND_KEY_BLOCK to "1")
            message.setExpansion(expansion)
            IMCenter.getInstance().refreshMessage(message)
        }

        override fun onError(errorCode: RongIMClient.ErrorCode?) {
            LogX.e("updateMessageExpansion error ${errorCode}")
        }
    }

    private val service: AnchorService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<GetRongYunTokenEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    //TODO 处理需要单独处理的消息
    private val mOnReceiveMessageListener: RongIMClient.OnReceiveMessageWrapperListener = object : RongIMClient.OnReceiveMessageWrapperListener() {
        override fun onReceived(
            message: Message, left: Int, hasPackage: Boolean, offline: Boolean
        ): Boolean {
            LogX.d("onReceivedMessageaa1  ：  " + message.content)
            LogX.d("onReceivedMessageaa2  ：  " + message.content.extra)
            LogX.d("onReceivedMessageaa3  ：  " + message.extra)
            LogX.d("onReceivedMessageaa4  ：  " + message.content.jsonDestructInfo)
            updateCommandMessage(message)
            updateCustomServiceCache(message)
            refreshUnreadMsg()
//                updateMikChatMessage(message, userInfo)
            updateAskGiftMikChatMessage(message)
            updateSystemMikChatMessage(message)
            updateTextMikChatMessage(message)
            return false
        }
    }

    private val translationResultListener = TranslationResultListener { code, result ->
        if (code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_INVALID_AUTH_TOKEN.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_AUTH_FAILED.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_SERVER_AUTH_FAILED.code) {
            getRongYunTranslateToken()
        }
    }

    // 静态内部类实现回调，避免内存泄漏
    private class UnReadCallback(viewModel: MainViewModel) : RongIMClient.ResultCallback<Int>() {
        private val weakViewModel = WeakReference(viewModel)

        override fun onSuccess(unReadCount: Int?) {
            val viewModel = weakViewModel.get() ?: return
            unReadCount?.let {
                viewModel.viewModelScope.launch {
                    viewModel._pageEvents.setEvent(GetRongYunTokenEvent.RefreshBottomBarUnReadMsg(it))
                }
            }
        }

        override fun onError(e: RongIMClient.ErrorCode?) {
            // 错误处理
        }
    }

    private var unReadCallback: UnReadCallback = UnReadCallback(this)

    init {
        IMCenter.getInstance().addAsyncOnReceiveMessageListener(mOnReceiveMessageListener) //融云消息监听
        TranslationClient.getInstance().addTranslationResultListener(translationResultListener)
        RongCoreClient.getInstance().setMessageBlockListener {//处理消息拦截违规
            LogX.e("vvvvvvvvvvvv ${it.targetId} ${it.type} ${it.blockMsgUId}")
            if (it.type == MessageBlockType.BLOCK_THIRD_PATY) {//三方数美判定照片违规
                // 使用静态内部类实现回调，避免内存泄漏
                RongCoreClient.getInstance().getMessageByUid(it.blockMsgUId, MessageBlockCallback())
            }
        }

        // 监听 WebSocket 消息
        viewModelScope.launch {
            WebSocketManager.getInstance().webSocketEventFlow.collect { event ->
                when (event) {
                    is WebSocketManager.WebSocketEvent.OnMessage -> {
                        try {
                            val webSocketResponse = event.text.fromJson<WebSocketResponse>()
                            if (webSocketResponse?.code == Constants.ErrorCode.TOKEN_EXPIRED) {
                                FlowBus.with<Int>(Constants.TOKEN_EXPIRED)
                                    .post(Constants.ErrorCode.TOKEN_EXPIRED)
                            } else {
                                dispatchMessage(event.text, webSocketResponse?.data?.seq, "websocket")
                            }
                        } catch (e: Exception) {
                            LogX.e("解析 WebSocket 消息失败，无法获取 seq: ${e.message}, 消息内容: ${event.text}")
                        }
                    }

                    else -> {
                        // 处理其他 WebSocket 事件，例如连接成功、关闭、失败等
                        LogX.d("WebSocketEvent: ${event.javaClass.simpleName}")
                    }
                }
            }
        }

    }

    private fun updateCustomServiceCache(message: Message) {
        if (message.senderUserId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
            AppUtil.cacheCustomServiceInfo()
        }
        if (message.senderUserId == Constants.RONG_YUN_ID_SYSTEM) {
            AppUtil.cacheSystemNoticeInfo()
        }
    }

//    private fun updateMikChatMessage(message: Message, userInfo: UserInfo?) {
//        if (message.content is MikChatMessage) {
//            val mikChatMessage: MikChatMessage = message.content as MikChatMessage
//            LogX.d("onReceivedMessageaa5  ：  type : ${mikChatMessage.type}  ${mikChatMessage.warnMsg}")
//            when (mikChatMessage.type) {
//                Constants.PUSH_TYPE_ANCHOR_REJECT -> {
//                    viewModelScope.launch {
//                        FlowBus.with<String>(Constants.PUSH_TYPE_ANCHOR_REJECT).post(mikChatMessage.channelId)
//                    }
//                }
//
//                Constants.PUSH_TYPE_ANCHOR_CANCEL -> {
//                    viewModelScope.launch {
//                        FlowBus.with<String>(Constants.PUSH_TYPE_ANCHOR_CANCEL).post(mikChatMessage.channelId)
//                    }
//                }
//
//                Constants.PUSH_TYPE_NOTIFY_CALL -> {
//                    if (!MMKVDataRep.doNotDisturb) {
//                        //TODO 需要再viewModel里跳转弹窗页面 flow和生命周期有绑定
//                        LogX.e("vvvvvvvvv ${userInfo?.userId} ${mikChatMessage.content}")
//                        var isNeedMute = false
//                        var isNeedBal = false
//                        var recordId = ""
//                        var videoPrice = Constants.VIDEO_DEFAULT_PRICE
//                        var url = ""
//                        var videoId = 0L
//                        if (mikChatMessage.content != null) {
//                            var jsonObj: JSONObject? = null
//                            try {
//                                jsonObj = JSONObject(mikChatMessage.content)
//                            } catch (e: JSONException) {
//                                throw RuntimeException(e)
//                            }
//                            if (jsonObj.has("isNeedMute")) {
//                                isNeedMute = jsonObj.optBoolean("isNeedMute")
//                            }
//                            if (jsonObj.has("isNeedBal")) {
//                                isNeedBal = jsonObj.optBoolean("isNeedBal")
//                            }
//                            if (jsonObj.has("videoPrice")) {
//                                videoPrice = jsonObj.optInt("videoPrice")
//                            }
//
//                            if (jsonObj.has("recordId")) {
//                                recordId = jsonObj.optString("recordId")
//                            }
//                            if (jsonObj.has("videoId")) {
//                                videoId = jsonObj.optLong("videoId")
//                            }
//                            if (jsonObj.has("url")) {
//                                url = jsonObj.optString("url")
//                            }
//                        }
//                        userInfo?.apply {
//                            val chatTipBean = ChatTipBean(
//                                userId, nickName = name ?: "", content = url, isNeedMute = isNeedMute, isNeedBal = isNeedBal, recordId = recordId, videoPrice = videoPrice, videoId = videoId
//                            )
//                            NotifyVideoManager.enqueueDownload(viewModelScope, chatTipBean)
//                        }
//                    }
//                }
//            }
//
//            return
//        }
//    }

//    fun test() {
//        viewModelScope.launch {
//            delay(2000)
//            val chatTipBean = ChatTipBean(
//                "1837424868711780354", nickName = "", content = "https://bdckj.s3.ap-southeast-1.amazonaws.com/1724297235077", isNeedMute = true, isNeedBal = true, recordId = "123", videoPrice = 50
//            )
//            NotifyVideoManager.enqueueDownload(viewModelScope, chatTipBean)
//        }
//    }

    private fun updateAskGiftMikChatMessage(message: Message) {
        if (message.content is MikChatAskGiftMessage) {
            val mikChatMessage: MikChatAskGiftMessage = message.content as MikChatAskGiftMessage
            LogX.d("onReceivedMessageaa6  ：  type : ${mikChatMessage.svga_url}")
            viewModelScope.launch {
                FlowBus.with<MikChatAskGiftMessage>(Constants.PUSH_TYPE_ANCHOR_REQUEST_GIFT).post(mikChatMessage)
            }
            return
        }
    }

    private fun updateSystemMikChatMessage(message: Message) {
        if (message.content is MikChatSystemMessage) {
            val mikChatMessage: MikChatSystemMessage = message.content as MikChatSystemMessage
            return
        }
    }

    private fun updateTextMikChatMessage(message: Message) {
        if (message.content is TextMessage) {
            viewModelScope.launch {
                message.content?.let {
                    val textMessage = message.content as TextMessage
                    val chatTipBean = ChatTipBean(
                        message.senderUserId, nickName = "", content = textMessage.content ?: ""
                    )
                    FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_TEXT_MESSAGE).post(chatTipBean)
                }
            }
        }
        return
    }

    fun fetchConfiguration() {
        viewModelScope.launch {
            val response = service.getSystemConfiguration().tryAwait {
                LogX.e("fetchConfiguration failed ${it.msg}")
            }
            response?.let {
                MMKVDataRep.configuration = it
            }
        }
    }

//    fun getRongYunToken() {
//        viewModelScope.launch {
//            val rongToken = service.getRongYunToken(AnchorParams.getRongYunTokenBody()).tryAwait {
//                _pageEvents.setEvent(GetRongYunTokenEvent.ShowToast(it.msg))
//            }
//            //连接融云
//            rongToken?.let {
//                MMKVBaseDataRep.rongYunToken = it //本地存储融云token
//                if (it.isNotEmpty()) {
//                    _pageEvents.setEvent(GetRongYunTokenEvent.GetRongYunTokenSuccess(it))
//                } else {
//                    LogX.e("getRongYunToken response null")
//                }
//            }
//        }
//    }

    fun getRongYunTranslateToken() {
        ktHttpRequest {
            val rongTraToken = service.getRongYunTranslateToken().await()
            //
            rongTraToken?.let {
                MMKVBaseDataRep.rongYunTransitionToken = it.translateToken //本地存储融云翻译token

                TranslationClient.getInstance().updateAuthToken(it.translateToken)
            }
        }
    }

    fun clearCacheVideos(){
        viewModelScope.launch(Dispatchers.IO) {
            DownloadVideoManager.cleanupDownloadedVideos()
        }
    }

    /**
     * 获取预热视频并下载
     * 逻辑描述，服务端每次都会返回一组视频 作为匹配的备用虚拟视频 客户端递归下载
     * 在匹配时，后端可能会返回这一组的虚拟视频并标记为已播放。当这一组播放完成 下次会返回新的一组
     */
    fun getVirtualVideos() {
        viewModelScope.launch {
            //删除缓存视频
            MMKVDataRep.cacheFilePath?.let {
                if (it.isNotEmpty()) {
                    val file = File(it)
                    if (file.exists()) {
                        file.delete()
                    }
                }
            }
//            val videos = service.getVirtualVideos().tryAwait {
//                LogX.e("getVirtualVideos failed ${it.msg}")
//            }
//            videos?.let {
//                if (it.isNotEmpty()) {
//                    //去掉本地已经下载的视频  这里没有md5 无法做视频完整性校验
//                    val cacheFolder = File(ContextHolder.context.filesDir, SAVE_VIDEO_FOLDER)
//                    if (cacheFolder.exists()) {
//                        val savedUrl = mutableListOf<VirtualVideoBean>() // 已经下载的视频
//                        it.forEach { bean ->
//                            if (File(cacheFolder, "${bean.id}.mp4").exists()) {
//                                savedUrl.add(bean)
//                            }
//                        }
//                        it.removeAll(savedUrl)
//                    }
//                    if (it.isNotEmpty()) {
//                        withContext(Dispatchers.IO) {
//                            VirtualVideoManager.enqueueDownload(it)
//                        }
//                    }
//                }
//            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        //释放自定义消息接受 防止内存泄露
        IMCenter.getInstance().removeAsyncOnReceiveMessageListener(mOnReceiveMessageListener)
        TranslationClient.getInstance().removeTranslationResultListener(translationResultListener)

        // 清理未读消息回调
        RongIMClient.getInstance().getTotalUnreadCount(null)

        // 清理消息块监听器
        RongCoreClient.getInstance().setMessageBlockListener(null)

        // 取消所有正在进行的协程
//        requestIncomingAnchorJob?.cancel()

        // 尝试触发垃圾回收
        try {
            System.gc()
        } catch (e: Exception) {
            LogX.e("Error during cleanup: ${e.message}")
        }
    }

//    fun fetchAndInsertWords() {
//        viewModelScope.launch {
//            val wordDao = AppDatabase.getInstance(ContextHolder.context).wordDao()
//            LogX.i("fetchAndInsertWords ${wordDao.getWordCount()}")
//            if (wordDao.getWordCount() > 0) {
//                WordFilter.getInstance().initializeTrie()
//            } else {
//                val words = service.getSensitiveWords().tryAwait {
////                        LogX.e("fetchConfiguration failed ${it.msg}")
//                }
//                words?.let {
//                    if (it.isNotEmpty()) {
//                        LogX.i("fetchAndInsertWords response: ${it.size}")
//                        withContext(Dispatchers.IO) {
//                            wordDao.insertWords(it)
//                            WordFilter.getInstance().initializeTrie(it)
//                        }
//                    }
//                }
//            }
//        }
//    }

    fun refreshUnreadMsg() {
        RongIMClient.getInstance().getTotalUnreadCount(unReadCallback)
    }

//    fun startAutoAnchorCalling() {
//        viewModelScope.launch {
//            DiamondChangeManager.sharedValueFlow.collect { diamond ->
//                //用户有钻石 或者 用户充值了 开始轮询
//                if (diamond > (MMKVDataRep.configuration?.anchorDefaultCallAmount?.toInt()
//                        ?.applyVIPPrice() ?: Constants.VIDEO_DEFAULT_PRICE)
//                ) {
//                    IncomingAnchorManager.startLoopIncomingAnchor(viewModelScope) {
//                        realAutoCalling()
//                    }
//                    delay(1000)
//                    //如果开启了免打扰挂起循环
//                    // TODO 这里这么设计，不在一开始判断开启免打扰就不开启循环，是为了后续用户修改免打扰状态时能很方便的挂起和还原循环
//                    if (MMKVDataRep.doNotDisturb) {
//                        IncomingAnchorManager.pauseLoopIncomingAnchor()
//                    }
//                } else {
//                    //用户没钱 或者 中途没钱了 关闭轮询
//                    IncomingAnchorManager.stopLoopIncomingAnchor()
//                }
//            }
//        }
//    }

//    private var requestIncomingAnchorJob: Job? = null
//    private fun realAutoCalling() {
//        requestIncomingAnchorJob?.cancel() // 新的来电请求 取消上一个请求
//        requestIncomingAnchorJob = viewModelScope.launch {
//            val anchorInfo = service.incomingAnchor(AnchorParams.getRongYunTokenBody()).tryAwait {
//                LogX.e("incomingAnchor failed: ${it.code}, ${it.message} ")
//            }
//            anchorInfo?.let {
//                if (it.id.isNotEmpty()) {
//                    ContextHolder.context.getLifecycleCallbacks()?.apply {
//                        if (AppUtil.canAutoPopupVideoCallingPage) {
//                            AppUtil.canAutoPopupVideoCallingPage = false
//                            if (activityList.isNotEmpty()) {
//                                val activity = activityList[0]
//                                val intent = Intent(activity, AnchorVideoActivity::class.java)
//                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                                intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.toJson())
//                                intent.putExtra(
//                                    Constants.INTENT_PARAM_IS_INCOMING_CALLING, true
//                                )//是否是来电主播
//                                intent.putExtra(
//                                    Constants.INTENT_PARAM_VIDEO_SOURCE,
//                                    Constants.VIDEO_SOURCE_INCOMING_AIB
//                                )//来源自动拨打
//                                activity.startActivity(intent)
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }

    private fun updateCommandMessage(message: Message) {
        if (message.content is CommandMessage) {
            val commandMessage: CommandMessage = message.content as CommandMessage
            LogX.i("commandMessage: ${commandMessage.name}     ${commandMessage.data}")
            // 尝试从 commandMessage.data 中解析出 cmd 和 seq
            try {
                val dataJson = commandMessage.data
                val webSocketMessage = dataJson.fromJson<WebSocketMessage>()
                val cmd = webSocketMessage?.cmd
                val seq = webSocketMessage?.seq

                if (cmd != null && seq != null) {
                    // 封装成 WebSocketResponse 格式的 JSON 字符串
                    val fullMessageJson = "{\"code\":0,\"data\":$dataJson,\"message\":\"\"}"
                    dispatchMessage(fullMessageJson, seq, "RongYun")
                } else {
                    LogX.e("无法从 CommandMessage.data 中解析 cmd 或 seq: $dataJson")
                }
            } catch (e: Exception) {
                LogX.e("解析 CommandMessage.data 失败: ${e.message}")
            }
        }
    }

    /**
     * 统一处理和分发 WebSocket 消息，并进行去重
     * @param messageJson 消息的 JSON 字符串
     * @param seq 消息的唯一序列号，用于去重。如果为 null，则不进行去重。
     */
    private fun dispatchMessage(messageJson: String, seq: String? = null, fromType: String?) {

        LogX.e("dispatchMessage:  $fromType $seq")
        if (seq != null) {
            if (processedMessageSeqs.containsKey(seq)) {
                LogX.d("消息已处理，跳过重复消息: fromtype=$fromType  seq=$seq")
                return
            }
            processedMessageSeqs[seq] = System.currentTimeMillis()
        }

        try {
            val webSocketResponse = messageJson.fromJson<WebSocketResponse>()
            val webSocketMessage = webSocketResponse?.data
            if (webSocketMessage == null) {
                LogX.e("解析 WebSocket 消息为 null 或 data 字段为 null: $messageJson")
                return
            }
            when (webSocketMessage.cmd) {
                Constants.PushCmd.PUSH_CMD_REMOTE_CALL_REFUSE -> {
                    val callRefuseExtend = webSocketMessage.extend?.fromJson<RemoteCallRefuseExtend>()
                    if (callRefuseExtend != null) {
                        handleRemoteCallRefuse(callRefuseExtend)
                    } else {
                        LogX.e("解析 remote_call_refuse 的 extend 失败: ${webSocketMessage.extend}")
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_USER_GOLD_CHANGE -> {
                    val callGoldChangeExtend = webSocketMessage.extend?.fromJson<RemoteCallGoldChangeExtend>()
                    if (callGoldChangeExtend != null) {
                        //余额变化
                        DiamondChangeManager.updateDiamond(callGoldChangeExtend.behindGold)
                    } else {
                        LogX.e("解析 remote_call_gold_change 的 extend 失败: ${webSocketMessage.extend}")
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_USER_CLEAN_MATCH -> { //匹配次数变化
                    val userCleanMatchExtend = webSocketMessage.extend?.fromJson<RemoteUserCleanMatchExtend>()
                    if (userCleanMatchExtend != null) {
                        viewModelScope.launch {
                            FlowBus.with<RemoteUserCleanMatchExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_USER_CLEAN_MATCH).post(userCleanMatchExtend)
                        }
                    } else {
                        LogX.e("解析 remote_user_clean_match 的 extend 失败: ${webSocketMessage.extend}")
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_SOCKET_STATUS_CHANGE -> {
                    val socketStatusChangeExtend = webSocketMessage.extend?.fromJson<RemoteSocketStatusChangeExtend>()
                    if (socketStatusChangeExtend != null) {
                        handleRemoteStatusChange(socketStatusChangeExtend)
                    } else {
                        LogX.e("解析 remote_socket_status_change 的 extend 失败: ${webSocketMessage.extend}")
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_NOTIFY_WINDOW -> {
                    val remoteNotifyWindowChangeExtend = webSocketMessage.extend?.fromJson<RemoteNotifyWindowChangeExtend>()
                    if (remoteNotifyWindowChangeExtend != null) {
                        handleRemoteNotifyWindow(remoteNotifyWindowChangeExtend)
                    } else {
                        LogX.e("解析 remote_call_gold_change 的 extend 失败: ${webSocketMessage.extend}")
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_USER_TASK_FINISH -> { //任务完成收到的socket消息
                    viewModelScope.launch {
                        FlowBus.with<Boolean>(Constants.PushCmd.PUSH_CMD_REMOTE_USER_TASK_FINISH).post(true)
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_USER_VIP_CHANGE -> {
                    viewModelScope.launch {
                        _pageEvents.setEvent(GetRongYunTokenEvent.VipChangeRefresh)
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_PULL_LOG -> {
                    LogX.forceUpload()
                }

                else -> {
                    LogX.d("未知 WebSocket 消息类型: ${webSocketMessage.cmd}")
                }
            }
        } catch (e: Exception) {
            LogX.e("解析 WebSocket 消息失败: ${e.message}, 消息内容: $messageJson")
        }
    }

    /**
     * 用户收到主播的拒绝
     */
    private fun handleRemoteCallRefuse(extend: RemoteCallRefuseExtend) {
        val remoteCallRefuseExtend = extend.toJson()
        LogX.d("RemoteCallInviteExtend JSON: $remoteCallRefuseExtend")
        viewModelScope.launch {
            FlowBus.with<RemoteCallRefuseExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_CALL_REFUSE).post(extend)
        }
    }

    /**
     * 在线变化
     */
    private fun handleRemoteStatusChange(extend: RemoteSocketStatusChangeExtend) {
        val remoteSocketStatusChangeExtend = extend.toJson()
        LogX.d("remoteSocketStatusChangeExtend JSON: $remoteSocketStatusChangeExtend")
        viewModelScope.launch {
            FlowBus.with<RemoteSocketStatusChangeExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_SOCKET_STATUS_CHANGE).post(extend)
        }
    }

    /**
     *  "type":1,//1通话相关 2 资源缓存 3更新提醒
     *   "call_type":1, //1虚拟视频 2 假aib 3真aib
     */
    private fun handleRemoteNotifyWindow(extend: RemoteNotifyWindowChangeExtend) {
        when (extend.type) {
            1 -> {
                when (extend.callType) {
                    1 -> {
                        //虚拟视频
                        val fileName = "${extend.url.toMD5()}.mp4"
                        val file = File(DownloadVideoManager.getVideoFolder(), fileName)
                        if (MMKVDataRep.doNotDisturb || !AppUtil.canAutoPopupVideoCallingPage) {
                            if (file.exists()) {
                                file.delete()
                            }
                            return
                        }

                        val videoUrl = if (file.exists()) {
                            file.path
                        } else {
                            extend.url
                        }
                        LogX.i("VideoPlaybackController playVideo $videoUrl")
                        ContextHolder.context.getLifecycleCallbacks()?.apply {
                            if (activityList.isNotEmpty()) {
                                val activity = activityList[0]
                                if (activity is AnchorVideoActivity) {
                                    LogX.i("AnchorVideoActivity is top")
                                } else {
                                    // 通过 Intent 启动播放器页面，并传递视频的 URI
                                    val intent = Intent(ContextHolder.context, IncomingVideoActivity::class.java)
                                    val tmpUser = UserBean(id = extend.anchorId, nickname = extend.nickname, avatar = extend.anchorAvatar)
                                    intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, tmpUser.toJson())
                                    intent.putExtra(Constants.INTENT_PARAM_VIDEO_URL, videoUrl)
                                    intent.putExtra(Constants.INTENT_PARAM_ISNEEDMUTE, !extend.openVoice)
                                    intent.putExtra(Constants.INTENT_PARAM_ISNEEDBAL, false)
//                                                intent.putExtra(Constants.INTENT_PARAM_VIDEO_PRICE, videoPlayBean.videoPrice)
                                    intent.putExtra(Constants.INTENT_PARAM_RECORD_ID, extend.recordId)
                                    intent.putExtra(Constants.INTENT_PARAM_VIDEO_DURATION, extend.duration)
//                                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                    activity.startActivity(intent)
                                }
                            }
                        }
                    }

                    2 -> { //假aib  点击接听弹支付
                        if (MMKVDataRep.doNotDisturb) {
                            return
                        }
                        val it = UserBean(id = extend.anchorId, nickname = extend.nickname, avatar = extend.anchorAvatar)
                        ContextHolder.context.getLifecycleCallbacks()?.apply {
                            if (AppUtil.canAutoPopupVideoCallingPage) {
                                if (activityList.isNotEmpty()) {
                                    val activity = activityList[0]
                                    if (activity is IncomingVideoActivity) {
                                        LogX.i("IncomingVideoActivity is top")
                                    } else {
                                        val intent = Intent(activity, AnchorVideoActivity::class.java)
//                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.toJson())
                                        intent.putExtra(Constants.INTENT_PARAM_IS_INCOMING_CALLING, true)//是否是来电主播
                                        intent.putExtra(Constants.INTENT_PARAM_VIDEO_SOURCE, Constants.VIDEO_SOURCE_INCOMING_AIB)//来源自动拨打
                                        intent.putExtra(Constants.INTENT_PARAM_ISNEEDBAL, true)//来源自动拨打
                                        intent.putExtra(Constants.INTENT_PARAM_RECORD_ID, extend.recordId)
                                        activity.startActivity(intent)
                                    }
                                }
                            }
                        }
                    }

                    3 -> { //真aib 点击接听创建频道
                        if (MMKVDataRep.doNotDisturb) {
                            return
                        }
                        val it = UserBean(id = extend.anchorId, nickname = extend.nickname, avatar = extend.anchorAvatar)
                        ContextHolder.context.getLifecycleCallbacks()?.apply {
                            if (AppUtil.canAutoPopupVideoCallingPage) {
                                if (activityList.isNotEmpty()) {
                                    val activity = activityList[0]
                                    if (activity is IncomingVideoActivity) {
                                        LogX.i("IncomingVideoActivity is top")
                                    } else {
                                        val intent = Intent(activity, AnchorVideoActivity::class.java)
//                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, it.toJson())
                                        intent.putExtra(Constants.INTENT_PARAM_IS_INCOMING_CALLING, true)//是否是来电主播
                                        intent.putExtra(Constants.INTENT_PARAM_VIDEO_SOURCE, Constants.VIDEO_SOURCE_INCOMING_AIB)//来源自动拨打
                                        activity.startActivity(intent)
                                    }
                                }
                            }
                        }
                    }
                }
            }

            2 -> {
                //资源缓存 下载视频
                if (extend.url.isNotEmpty()) {
                    DownloadVideoManager.enqueueDownload("MainViewModel", extend.url, object : DownloadVideoManager.DownloadListener {
                        override fun onStart(url: String) {
                            LogX.d(" MainViewModel Download started: $url")
                        }

                        override fun onProgress(url: String, currentOffset: Long, totalLength: Long, progress: Int) {
//                            LogX.d("MainViewModel Download progress: $url, $progress%")
                        }

                        override fun onComplete(url: String, localPath: String) {
                            LogX.i("MainViewModel download video success: $localPath")
                        }

                        override fun onError(url: String, error: String) {
                            LogX.e("MainViewModel download video error: $error")
                        }

                        override fun onCancel(url: String) {
                            LogX.d(" MainViewModel Download canceled: $url")
                        }
                    })
                }
            }
        }
    }
}

sealed class GetRongYunTokenEvent {
    data class ShowToast(val message: String) : GetRongYunTokenEvent()
//    data class GetRongYunTokenSuccess(val token: String) : GetRongYunTokenEvent()
    data class RefreshBottomBarUnReadMsg(val count: Int) : GetRongYunTokenEvent()
    data object VipChangeRefresh : GetRongYunTokenEvent()
}