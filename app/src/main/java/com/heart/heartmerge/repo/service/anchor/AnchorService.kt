package com.heart.heartmerge.repo.service.anchor

import com.bdc.android.library.ktnet.coroutines.Await
import com.heart.heartmerge.beans.AnchorList
import com.heart.heartmerge.beans.AvailableTimeBean
import com.heart.heartmerge.beans.CallSettlementBean
import com.heart.heartmerge.beans.ChannelBean
import com.heart.heartmerge.beans.ConfigurationBean
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.LanguageBean
import com.heart.heartmerge.beans.MatchInfoBean
import com.heart.heartmerge.beans.PageBean
import com.heart.heartmerge.beans.PageResponse
import com.heart.heartmerge.beans.RateLabelList
import com.heart.heartmerge.beans.RongYunTraToken
import com.heart.heartmerge.beans.TabBean
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoHistoryList
import com.heart.heartmerge.http.ResultX
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface AnchorService {

    @GET("/api/v1/user/homepage")
    suspend fun getHomepage(
        @Query("home_page_type") type: Int,
        @Query("cursor") cursor: String,
        @Query("size") pageSize: Int
    ): Await<AnchorList?>

    @POST("/matechat/anchor/recommend")
    suspend fun getRecommend(@Body params: Map<String, String>): ResultX<PageBean<UserBean>>

    @POST("/matechat/anchor/popular")
    suspend fun getPopular(@Body body: RequestBody): Await<AnchorList?> //热门主播列表

    @POST("/matechat/anchor/new")
    suspend fun getNewList(@Body body: RequestBody): Await<AnchorList?> //新主播列表

    @POST("/matechat/anchor/follow")
    suspend fun getFollowList(@Body body: RequestBody): Await<AnchorList?> //关注主播列表

    @GET("/api/v1/user/anchor/detail")
    suspend fun getAnchorDetail(@Query("anchor_id") anchorId: String): Await<UserBean>

    @GET("mateuser/auth/language/list")
    suspend fun getLanguages(): Await<MutableList<LanguageBean>?>

    @POST("/api/v1/user/call/create")
    suspend fun genVideoChannel(@Body body: RequestBody): Await<ChannelBean?> //生成频道号

    @POST("/api/v1/user/gift/send")
    suspend fun postGiftGive(@Body body: RequestBody): Await<Any?> //送礼物

    @GET("/matechat/video/call/settlement")
    suspend fun getCallSettlement(@Query("channelId") channelId: String): Await<CallSettlementBean?>

    @GET("/api/v1/user/match")
    suspend fun getRandomAnchor(): Await<MatchInfoBean?>

    @POST("/matechat/video/matching/history/list")
    suspend fun getMatchingHistory(@Body body: RequestBody): Await<VideoHistoryList?>

    @GET("/api/v1/user/get_translate_token")
    suspend fun getRongYunTranslateToken(): Await<RongYunTraToken?>

    @POST("/matechat/anchor/list/like/mind")
    suspend fun searchAnchor(@Body body: RequestBody): Await<AnchorList?> //根据主播id（code）- 名称 模糊查询主播列表

    @GET("/matechat/gift/income/list")
    suspend fun getAnchorGift(@Query("anchorId") anchorId: String): Await<MutableList<GiftItemBean>?> //查询主播礼物收入列表

    @POST("/api/v1/user/relation/follow/op")
    suspend fun follow(@Body body: RequestBody): Await<Any>

    @POST("/api/v1/user/relation/cancel_follow/op")
    suspend fun unFollow(@Body body: RequestBody): Await<Any>

    @GET("/api/v1/user/call_record/list")
    suspend fun videoHistory(@Query("cursor") cursor: String, @Query("size") pageSize: Int): Await<VideoHistoryList?>

    @POST("/matechat/video/incoming/anchor")
    suspend fun incomingAnchor(@Body body: RequestBody): Await<UserBean?>

    @POST("/api/v1/user/relation/block/op")
    suspend fun black(@Body body: RequestBody): Await<Any?>

    @GET("/mateuser/auth/sys/param")
    suspend fun getSystemConfiguration(): Await<ConfigurationBean?>

    @GET("/api/v1/user/call/tag_list")
    suspend fun getRateLabels(): Await<MutableList<RateLabelList>?>

    @POST("/api/v1/user/call/mark_score")
    suspend fun anchorRate(@Body body: RequestBody): Await<Any?>

    @GET("/matechat/gift/large/list")
    suspend fun getLargeGift(): ResultX<List<GiftItemBean>>

    @POST("/api/v1/user/album/unlock")
    suspend fun userAlbumPay(@Body body: RequestBody): Await<Any?>

    @POST("/api/v1/user/anchor/list")
    suspend fun getAnchorList(@Body body: RequestBody): Await<PageResponse<UserBean>?>

    @GET("/api/v1/user/call/available_time")
    suspend fun getAvailableTime(@Query("call_id") callId: String): Await<AvailableTimeBean?>

    @POST("/api/v1/user/aiv/record")
    suspend fun aivRecord(@Body body: RequestBody): Await<Any?>

    @POST("/api/v1/user/aiv/start")
    suspend fun aivStart(@Body body: RequestBody): Await<Any?>

    @POST("/api/v1/user/aiv/refuse")
    suspend fun aivRefuse(@Body body: RequestBody): Await<Any?>

    @GET("/api/v1/user/tab")
    suspend fun getTabs(): Await<PageResponse<TabBean>?>
}