package com.heart.heartmerge.socket

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.Uri
import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.ktnet.utils.NetKey
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.AppManager
import com.google.gson.Gson
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.UniqueIDUtil
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import java.util.Locale
import java.util.concurrent.TimeUnit
import kotlin.collections.iterator

class WebSocketManager private constructor() : DefaultLifecycleObserver {
    private var okHttpClient: OkHttpClient? = null
    private var webSocket: WebSocket? = null
    private var request: Request? = null
    private var isConnect = false
    private var isConnecting = false // 新增变量，表示是否正在连接中
    private var connectUrl: String? = null
    private var applicationContext: Context? = null

    private val gson = Gson()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 客户端心跳包时间为x秒 （暂定30s）
    private val CLIENT_HEARTBEAT_INTERVAL_SECONDS = 30L

    // 服务端心跳包时间为y秒 （暂定 20s）
    private val SERVER_HEARTBEAT_INTERVAL_SECONDS = 20L

    // 客户端每 x/2(即15秒) 发送一次心跳包
    private val CLIENT_SEND_HEARTBEAT_DELAY_MS = CLIENT_HEARTBEAT_INTERVAL_SECONDS / 2 * 1000
    private var currentClientHeartbeatDelayMs = CLIENT_SEND_HEARTBEAT_DELAY_MS
    private var isAppForeground = false // 新增变量，用于判断应用是否在前台

    // 如果客户端在 (即30s)内，一次都没有收到服务端的心跳包，则自动断开链接；
    private val CLIENT_RECEIVE_HEARTBEAT_TIMEOUT_MS = CLIENT_HEARTBEAT_INTERVAL_SECONDS * 1000

    private var lastClientHeartbeatSendTime = 0L
    private var lastServerHeartbeatReceiveTime = 0L
    private var reconnectAttempts = 0
    private val MAX_RECONNECT_DELAY_MS = 60 * 1000L // 最大重连延迟 60 秒
    private val MAX_RECONNECT_ATTEMPTS = 10 // 最大重连尝试次数

    private var clientHeartbeatJob: Job? = null
    private var serverHeartbeatCheckJob: Job? = null
    private var reconnectJob: Job? = null
    private var backgroundDisconnectJob: Job? = null

    private val BACKGROUND_DISCONNECT_DELAY_MS = 60 * 1000L // 后台1分钟后断开

    private var currentCallID: Int = 0 // 当前通话ID
    private var currentPage: String = "" // 当前所在的页面
    private var lastAppActionTimestamp: Long = 0L // 上次app操作的毫秒时间戳，全局值

    private val _webSocketEventFlow = MutableSharedFlow<WebSocketEvent>(extraBufferCapacity = 1)
    val webSocketEventFlow: SharedFlow<WebSocketEvent> = _webSocketEventFlow

    sealed class WebSocketEvent {
        data class OnOpen(val webSocket: WebSocket, val response: Response) : WebSocketEvent()
        data class OnMessage(val webSocket: WebSocket, val text: String) : WebSocketEvent()
        data class OnMessageBytes(val webSocket: WebSocket, val bytes: ByteString) : WebSocketEvent()
        data class OnClosing(val webSocket: WebSocket, val code: Int, val reason: String) : WebSocketEvent()
        data class OnClosed(val webSocket: WebSocket, val code: Int, val reason: String) : WebSocketEvent()
        data class OnFailure(val webSocket: WebSocket, val t: Throwable, val response: Response?) : WebSocketEvent()
    }

    fun init(context: Context, url: String) {
        this.connectUrl = url
        okHttpClient = OkHttpClient.Builder()
            .readTimeout(CLIENT_RECEIVE_HEARTBEAT_TIMEOUT_MS, TimeUnit.MILLISECONDS) // 设置读取超时，与客户端接收心跳超时时间一致
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .pingInterval(currentClientHeartbeatDelayMs, TimeUnit.MILLISECONDS) // OkHttp自带的ping机制，可以辅助心跳
            .build()
        LogX.e("Authorization: ${MMKVBaseDataRep.token}")
        val queryParams = mapOf(
            NetKey.KEY_OS_TYPE to "1",
            NetKey.KEY_DEVICE_ID to UniqueIDUtil.getUniqueID(AppManager.getApplication()),
            NetKey.KEY_DEVICE_MODEL to Build.MODEL,
            NetKey.KEY_OS_VERSION to Build.VERSION.RELEASE,
            NetKey.KEY_PACKAGE_NAME to BuildConfig.APPLICATION_ID,
            NetKey.KEY_VERSION to "${BuildConfig.VERSION_CODE}",
            NetKey.KEY_LANG to Locale.getDefault().language,
            NetKey.KEY_UID to MMKVDataRep.userInfo.id,
            NetKey.KEY_APP_ID to MMKVDataRep.userInfo.app_id.toString(),
        )

        LogX.e("vvvvvvvvvvvvvvvvvvvvvv  $queryParams")

        val fullUrl = buildWebSocketUrl(url, queryParams)

        request = Request.Builder().url(fullUrl).addHeader(Constants.TOKEN, MMKVBaseDataRep.token ?: "").build()
        this.applicationContext = context.applicationContext // 使用 applicationContext 防止内存泄漏
        // 注册生命周期观察者
        ProcessLifecycleOwner.Companion.get().lifecycle.addObserver(this)
        registerNetworkCallback()
    }

    fun buildWebSocketUrl(
        baseUrl: String,
        queryParams: Map<String, String>
    ): String {
        val uriBuilder = Uri.parse(baseUrl).buildUpon()
        for ((key, value) in queryParams) {
            uriBuilder.appendQueryParameter(key, value)
        }
        return uriBuilder.build().toString()
    }


    fun connect() {
        if (isConnect || isConnecting) { // 检查是否已连接或正在连接
            LogX.d(TAG, "WebSocket已连接或正在连接中，无需重复操作")
            return
        }
        if (connectUrl == null || request == null || okHttpClient == null) {
            LogX.e(TAG, "WebSocketManager未初始化，请先调用init方法")
            return
        }

        isConnecting = true // 设置正在连接状态
        reconnectJob?.cancel() // 取消任何待处理的重连任务

        webSocket = okHttpClient?.newWebSocket(request!!, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                super.onOpen(webSocket, response)
                isConnect = true
                isConnecting = false // 连接成功，重置正在连接状态
                lastClientHeartbeatSendTime = System.currentTimeMillis()
                lastServerHeartbeatReceiveTime = System.currentTimeMillis()
                reconnectAttempts = 0 // 连接成功，重置重连尝试次数
                currentClientHeartbeatDelayMs = CLIENT_SEND_HEARTBEAT_DELAY_MS // 重置心跳延迟
                backgroundDisconnectJob?.cancel() // 连接成功，取消后台断开任务
                startHeartbeatJobs()
                scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnOpen(webSocket, response)) }
                LogX.d(TAG, "WebSocket连接成功")
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                super.onMessage(webSocket, text)
                LogX.d(TAG, "收到消息: $text")
                // 检查是否是心跳包
                if (isHeartbeatMessage(text)) {
                    lastServerHeartbeatReceiveTime = System.currentTimeMillis()
                    LogX.d(TAG, "收到服务端心跳包")
                } else {
                    scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnMessage(webSocket, text)) }
                }
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                super.onMessage(webSocket, bytes)
                scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnMessageBytes(webSocket, bytes)) }
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                isConnect = false
                cancelHeartbeatJobs()
                scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnClosing(webSocket, code, reason)) }
                LogX.d(TAG, "WebSocket连接关闭中: code=$code, reason=$reason")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                isConnect = false
                cancelHeartbeatJobs()
                scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnClosed(webSocket, code, reason)) }
                LogX.d(TAG, "WebSocket连接已关闭: code=$code, reason=$reason")
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                super.onFailure(webSocket, t, response)
                isConnect = false
                isConnecting = false // 连接失败，重置正在连接状态
                cancelHeartbeatJobs()
                scope.launch { _webSocketEventFlow.emit(WebSocketEvent.OnFailure(webSocket, t, response)) }
                LogX.e(TAG, "WebSocket连接失败: ${t.message}")
                // 只有当应用在前台且网络可用时才尝试重连
                if (isAppForeground && isNetworkAvailable()) {
                    reconnect()
                } else {
                    LogX.d(TAG, "应用在后台或网络不可用，不进行重连。")
                }
            }
        })
    }

    fun disconnect() {
        if (webSocket != null) {
            webSocket?.close(1000, "主动断开连接")
            webSocket = null // 确保webSocket实例被置为null
        }
        isConnect = false
        isConnecting = false // 断开连接时，重置正在连接状态
        cancelHeartbeatJobs()
        reconnectJob?.cancel()
        backgroundDisconnectJob?.cancel() // 取消后台断开任务
        LogX.d(TAG, "WebSocket已断开")
    }

    fun sendMessage(message: String): Boolean {
        if (isConnect && webSocket != null) {
            return webSocket!!.send(message)
        }
        LogX.e(TAG, "WebSocket未连接，无法发送消息")
        return false
    }

    fun updateHeartbeatData(callID: Int) { // 移除 lastActionAt 参数
        getInstance().currentCallID = callID
        LogX.d(TAG, "更新心跳数据: callID=$callID")
    }
    fun updateHeartbeatDataPage(currentPage: String) {
        getInstance().currentPage = currentPage
        LogX.d(TAG, "更新心跳数据: currentPage=$currentPage")
    }

    fun updateLastAppActionTimestamp(timestamp: Long) {
        getInstance().lastAppActionTimestamp = timestamp
        LogX.d(TAG, "全局触摸事件更新时间戳: $timestamp")
    }

    private fun sendHeartbeat() {
        val extendData = mutableMapOf<String, Any?>()
        if (currentCallID != 0) {
            extendData["callID"] = currentCallID
        }
        if (currentPage.isNotEmpty()){
            extendData["page_name"] = currentPage
        }
        // last_action_at 是全局值，直接使用
        extendData["last_action_at"] = lastAppActionTimestamp.toString()
        //avail_popup 可以调度弹窗 true 不可调度弹窗false
        extendData["avail_popup"] = AppUtil.canAutoPopupVideoCallingPage
        extendData["in_app"] = isAppForeground

        val extendJson = if (extendData.isNotEmpty()) gson.toJson(extendData) else null
        val heartbeatMessage = HeartbeatData("heartbeat", MMKVDataRep.userInfo.id.toLong(), MMKVBaseDataRep.token ?: "", System.currentTimeMillis().toString(), extendJson)
        val json = gson.toJson(heartbeatMessage)
        sendMessage(json)
        LogX.d(TAG, "发送客户端心跳包: $json")
    }

    private fun isHeartbeatMessage(message: String): Boolean {
        return try {
            val heartbeatMessage = gson.fromJson(message, HeartbeatMessage::class.java)
            if (heartbeatMessage.code == Constants.ErrorCode.TOKEN_EXPIRED) {
                LogX.e(TAG, "token过期，断开连接")
                disconnect()
                ActivityManager.current?.let {
                    it as AppCompatActivity
                }?.let {
                    it.lifecycleScope.launch {
                        FlowBus.with<Int>(Constants.TOKEN_EXPIRED)
                            .post(Constants.ErrorCode.TOKEN_EXPIRED)
                    }
                }
                return false
            }
            heartbeatMessage.code == 0 && heartbeatMessage.data?.cmd == "heartbeat"
        } catch (e: Exception) {
            false
        }
    }

    private fun startHeartbeatJobs() {
        clientHeartbeatJob?.cancel()
        clientHeartbeatJob = scope.launch {
            try {
                while (isActive) {
                    sendHeartbeat()
                    lastClientHeartbeatSendTime = System.currentTimeMillis()
                    delay(currentClientHeartbeatDelayMs)
                }
            } catch (e: CancellationException) {
                LogX.d(TAG, "clientHeartbeatJob 已取消: ${e.message}")
            } finally {
                LogX.d(TAG, "clientHeartbeatJob 结束")
            }
        }

        serverHeartbeatCheckJob?.cancel()
        serverHeartbeatCheckJob = scope.launch {
            try {
                while (isActive) {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastServerHeartbeatReceiveTime > CLIENT_RECEIVE_HEARTBEAT_TIMEOUT_MS) {
                        LogX.e(TAG, "长时间未收到服务端心跳，断开连接并尝试重连")
                        disconnect()
                        reconnect() // 触发重连
                        break // 断开连接后退出循环
                    }
                    delay(1000) // 每秒检查一次
                }
            } catch (e: CancellationException) {
                LogX.d(TAG, "serverHeartbeatCheckJob 已取消: ${e.message}")
            } finally {
                LogX.d(TAG, "serverHeartbeatCheckJob 结束")
            }
        }
    }

    // 实现 DefaultLifecycleObserver 的方法
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        isAppForeground = true // 应用进入前台
        LogX.d(TAG, "App进入前台")
        WebSocketMessageSender.sendCallStatingUpdateMessage(currentCallID, Constants.WebSocketParamValue.CALL_STATING_UPDATE_START_TYPE_FOREGROUND)
        backgroundDisconnectJob?.cancel() // 取消后台断开连接任务
        if (!isConnect) {
            connect() // 如果未连接，尝试重新连接
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        LogX.d(TAG, "App进入后台")
        isAppForeground = false // 应用进入后台
        WebSocketMessageSender.sendCallStatingUpdateMessage(currentCallID, Constants.WebSocketParamValue.CALL_STATING_UPDATE_START_TYPE_BACKGROUND)
        // 启动一个延迟任务，如果1分钟内没有回到前台，则断开连接（确保所有资源释放）
        backgroundDisconnectJob = scope.launch {
            delay(BACKGROUND_DISCONNECT_DELAY_MS)
            LogX.d(TAG, "App后台运行超过1分钟，断开WebSocket连接（延迟任务）")
            disconnect() // 再次调用disconnect确保所有资源清理
        }
    }

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            LogX.d(TAG, "网络已连接，尝试重连WebSocket")
            if (!isConnect) {
                connect()
            }
        }

        override fun onLost(network: Network) {
            super.onLost(network)
            LogX.d(TAG, "网络已断开，主动断开WebSocket连接")
            disconnect() // 网络断开时主动断开连接
        }
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = applicationContext?.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        if (connectivityManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                        capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            } else {
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                @Suppress("DEPRECATION")
                return activeNetworkInfo != null && activeNetworkInfo.isConnected
            }
        }
        return false
    }

    private fun registerNetworkCallback() {
        applicationContext?.let {
            val connectivityManager = it.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkRequest = NetworkRequest.Builder().addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET).build()
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
        }
    }

    private fun unregisterNetworkCallback() {
        applicationContext?.let {
            val connectivityManager = it.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            connectivityManager.unregisterNetworkCallback(networkCallback)
        }
    }

    // 增加一个释放资源的方法，可以在 Application.onTerminate() 中调用
    fun release() {
        disconnect()
        ProcessLifecycleOwner.Companion.get().lifecycle.removeObserver(this)
        unregisterNetworkCallback()
        scope.cancel() // 取消所有协程
        instance = null // 清除单例实例
    }

    private fun cancelHeartbeatJobs() {
        clientHeartbeatJob?.cancel()
        serverHeartbeatCheckJob?.cancel()
        clientHeartbeatJob = null
        serverHeartbeatCheckJob = null
    }

    private fun reconnect() {
        reconnectJob?.cancel() // 取消之前的重连任务
        reconnectJob = scope.launch {
            reconnectAttempts++
            if (reconnectAttempts > MAX_RECONNECT_ATTEMPTS) {
                LogX.e(TAG, "达到最大重连尝试次数，停止重连。")
                return@launch
            }

            // 第一次重连延迟 3 秒，之后指数增长
            val initialDelay = 3000L
            val delayTime = (initialDelay * Math.pow(2.0, (reconnectAttempts - 1).toDouble())).toLong()
                .coerceAtMost(MAX_RECONNECT_DELAY_MS)

            // 弱网优化：根据重连次数调整心跳间隔，避免频繁心跳加重网络负担
            currentClientHeartbeatDelayMs = (CLIENT_SEND_HEARTBEAT_DELAY_MS * (1 + reconnectAttempts * 0.5)).toLong()
                .coerceAtMost(CLIENT_HEARTBEAT_INTERVAL_SECONDS * 2 * 1000L) // 最多延长到客户端心跳间隔的2倍

            LogX.d(TAG, "尝试重连WebSocket... 第 $reconnectAttempts 次尝试，延迟 ${delayTime / 1000} 秒，当前心跳间隔 ${currentClientHeartbeatDelayMs / 1000} 秒")
            delay(delayTime)
            if (!isConnect) {
                connect()
            }
        }
    }

    fun isConnected(): Boolean {
        return isConnect
    }

    companion object {
        private const val TAG = "WebSocketManager"

        @Volatile
        private var instance: WebSocketManager? = null

        fun getInstance(): WebSocketManager {
            return instance ?: synchronized(this) {
                instance ?: WebSocketManager().also { instance = it }
            }
        }
    }

    // 心跳包数据结构
    data class HeartbeatMessage(
        val code: Int,
        val data: HeartbeatData?,
        val message: String
    )

    data class HeartbeatData(
        val cmd: String,
        val owner: Long,
        val token: String,
        val seq: String,
        val extend: String? = null // 新增 extend 字段，类型改为 String
    )
}