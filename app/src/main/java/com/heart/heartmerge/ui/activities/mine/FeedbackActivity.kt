package com.heart.heartmerge.ui.activities.mine

import android.os.Build
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.asLiveData
import androidx.recyclerview.widget.RecyclerView
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.dp2px
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.utils.ToastUtil
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.ActivityFeedbackBinding
import com.heart.heartmerge.databinding.ItemFeedbackFileBinding
import com.heart.heartmerge.utils.GlideImageEngine
import com.heart.heartmerge.viewmodes.FeedbackViewModel
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.style.PictureSelectorStyle
import com.luck.picture.lib.utils.PictureFileUtils
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File


/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/9/2 15:56
 * @description :意见反馈
 */
class FeedbackActivity : BaseCoreActivity<ActivityFeedbackBinding, FeedbackViewModel>() {

    private val items = arrayListOf<String>()
    override fun getLayoutId(): Int = R.layout.activity_feedback

    override fun initView() {
        super.initView()
        items.add("")
        mBinding.recyclerView.adapter = NineImageAdapter(items)
    }

    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickMultipleVisualMedia(9)) { it ->
            it.map { url ->
                PictureFileUtils.getPath(this@FeedbackActivity, url)
            }.let {
                handleUpload(it)
            }
        }

    override fun bindListener() {
        mBinding.btnSubmit.click {
            if (mBinding.etContent.text.isEmpty()) {
                ToastUtil.show(getString(R.string.feedback_content_empty))
                return@click
            }

            mViewModel.submit(JsonObject().apply {
                addProperty("event", "evaluation")
                addProperty("data", JsonObject().apply {
                    addProperty("content", mBinding.etContent.text.toString())
                    addProperty("is_google", true)
                    if (items.any { it.isNotEmpty() }) {
                        add("images", JsonArray().apply {
                            items.filter { it.isNotEmpty() }.forEach {
                                add(it)
                            }
                        })
                    }
                }.toString())
            }).asLiveData().observe(this) {
                ToastUtil.show(getString(R.string.feedback_submit_success))
                finish()
            }
        }
    }

    private fun choose() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable(
                this
            )
        ) {
            pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        } else {
            PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).setMaxSelectNum(9)
                .setImageEngine(GlideImageEngine()).isDisplayCamera(false)
                .setSelectorUIStyle(PictureSelectorStyle().apply {
                    titleBarStyle = titleBarStyle.apply {
                        titleBackgroundColor =
                            ContextCompat.getColor(this@FeedbackActivity, R.color.background)
                    }
                    selectMainStyle = selectMainStyle.apply {
                        selectTextColor =
                            ContextCompat.getColor(this@FeedbackActivity, R.color.colorAccent)
                    }
                    bottomBarStyle = bottomBarStyle.apply {
                        bottomNarBarBackgroundColor =
                            ContextCompat.getColor(this@FeedbackActivity, R.color.background)
                        bottomPreviewSelectTextColor =
                            ContextCompat.getColor(this@FeedbackActivity, R.color.color_B452FF)
                        bottomSelectNumResources = R.drawable.shape_9f2af8_50_20
                        bottomSelectNumTextColor = ContextCompat.getColor(
                            this@FeedbackActivity, com.bdc.android.library.R.color.white
                        )
                    }
                }).forResult(object : OnResultCallbackListener<LocalMedia?> {
                    override fun onResult(result: ArrayList<LocalMedia?>) {
                        result.map {
                            PictureFileUtils.getPath(
                                this@FeedbackActivity, it?.path?.toUri()
                            )
                        }.let {
                            handleUpload(it)
                        }
                    }

                    override fun onCancel() {
                    }
                })
        }
    }

    private fun handleUpload(paths: List<String>) {
        if (paths.isEmpty()) {
            return
        }
        showLoading("Loading...")
        var count = 0
        fun upload(path: String) {
            mViewModel.upload(path).asLiveData().observe(this) {
                count += 1
                items.add(items.lastIndex, it.accessUrl ?: "")
                mBinding.recyclerView.adapter?.notifyItemRangeChanged(
                    0, items.size
                )
                if (count == paths.size) {
                    stopLoading()
                }
            }
        }

        Luban.with(this).load(paths).ignoreBy(100)
            .setCompressListener(object : OnNewCompressListener {
                override fun onStart() {
                }

                override fun onSuccess(source: String?, compressFile: File?) {
                    compressFile?.let {
                        upload(it.path)
                    }
                }

                override fun onError(source: String?, e: Throwable?) {
                    source?.let {
                        upload(it)
                    }
                }
            }).launch()
    }

    inner class NineImageAdapter(val items: List<String>) :
        RecyclerView.Adapter<NineImageAdapter.ViewHolder>() {
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            return ViewHolder(layoutInflater.inflate(R.layout.item_feedback_file, parent, false))
        }

        override fun getItemCount(): Int = items.size

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            val binding = ItemFeedbackFileBinding.bind(holder.itemView)
            binding.ivClose.apply {
                makeVisible(item?.isNotEmpty() == true)
                click {
                    <EMAIL>(holder.layoutPosition)
                    mBinding.recyclerView.adapter?.notifyItemRemoved(holder.layoutPosition)
                }
            }
            binding.iv.apply {
                item.takeIf { it.isNotEmpty() }?.let {
                    ImageLoader.with(holder.itemView.context).load(it).radius(8F.dp2px()).into(this)
                } ?: kotlin.run {
                    this.setBackgroundResource(R.mipmap.ic_upload_image_placeholder)
                }
                click {
                    if (item?.isNullOrEmpty() == true) {
                        choose()
                    }
                }
            }
        }
    }
}