package com.heart.heartmerge.ui.fragments.mine

import CheckInView
import android.os.Bundle
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.asLiveData
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.putIndex
import com.bdc.android.library.extension.setDrawableLeft
import com.bdc.android.library.extension.toast
import com.bdc.android.library.imageloader.ImageLoader
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.StringUtil
import com.heart.heartmerge.R
import com.heart.heartmerge.databinding.FragmentMineBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.formatDate
import com.heart.heartmerge.extension.formatDateTime
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showUserSignPopup
import com.heart.heartmerge.ui.activities.WebViewActivity
import com.heart.heartmerge.ui.activities.WhoSeeMeActivity
import com.heart.heartmerge.ui.activities.login.LoginActivity
import com.heart.heartmerge.ui.activities.mine.BackpackActivity
import com.heart.heartmerge.ui.activities.mine.DiamondDetailActivity
import com.heart.heartmerge.ui.activities.mine.InvitationActivity
import com.heart.heartmerge.ui.activities.mine.LevelActivity
import com.heart.heartmerge.ui.activities.mine.MatchingCardActivity
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.activities.mine.ProfileActivity
import com.heart.heartmerge.ui.activities.mine.RaffleActivity
import com.heart.heartmerge.ui.activities.mine.SettingActivity
import com.heart.heartmerge.ui.activities.mine.TaskActivity
import com.heart.heartmerge.ui.activities.mine.WalletActivity
import com.heart.heartmerge.ui.theme.HeartMergeTheme
import com.heart.heartmerge.ui.theme.color_590C92
import com.heart.heartmerge.ui.widget.DailyGrowingNumber
import com.heart.heartmerge.ui.widget.DiamondComposeView
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.RequestParametersBuilder
import com.heart.heartmerge.viewmodes.UserViewModel

class MineFragment : BaseCoreFragment<FragmentMineBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.fragment_mine

    override fun onResume() {
        super.onResume()
        mViewModel.refreshUser()
    }

    override fun initView() {
        mViewModel.userBean.asLiveData().observe(this) {
            ImageLoader.with(this@MineFragment).load(it?.avatar?.buildImageUrl()).asAvatar()
                .into(mBinding.ivAvatar)
//            mBinding.labelVip.makeVisible(it.isVIP)
            mBinding.tvName.text = it?.nickname
            mBinding.tvId.text = "ID: ${it?.id}"
            mBinding.tvId.tag = it.id
            mBinding.tvAge.text = "${it?.age}"
            mBinding.tvCountry.text = it?.userCountry?.title
//            mBinding.labels.setLabels(it.languages.map { it.languageName })
            it.level_config?.let {
                if (it.level >= 0) {
                    mBinding.levelLabelView.current = it.level
                    mBinding.levelCardView.current = it.level
                }
            }

            if (it.isVIP) {
                when (it.user_vip?.level) {
                    1 -> {
                        mBinding.ivVipCard.setImageResource(R.mipmap.bg_mine_bronze_vip)
                        mBinding.tvVipType.text = it.user_vip.title ?: "Bronze-VIP"
                    }

                    2 -> {
                        mBinding.ivVipCard.setImageResource(R.mipmap.bg_mine_silver_vip)
                        mBinding.tvVipType.text = it.user_vip.title ?: "Silver-VIP"

                    }

                    3 -> {
                        mBinding.ivVipCard.setImageResource(R.mipmap.bg_mine_gold_vip)
                        mBinding.tvVipType.text = it.user_vip.title ?: "Gold-VIP"
                    }
                }
            } else {
                mBinding.tvVipType.text = getString(R.string.vip)
                mBinding.ivVipCard.setImageResource(R.mipmap.bg_mine_inactive_vip)
            }

            mBinding.tvVipValue.text = if (it.isVIP) {
                getString(R.string.membership_expired_on, it.user_vip?.expire_at?.formatDate())
            } else getString(R.string.vip_non_activated)
            mBinding.llMenuSign.makeVisible(!it.isBound)

            if (it.isExpired && it.vipExpireDay.isNotEmpty()) {
                mBinding.llVip.setBackgroundResource(R.mipmap.ic_mine_vip_card_expired)
                mBinding.tvVipTitle.apply {
                    setDrawableLeft(R.mipmap.ic_diamond, 5)
                    text = getString(R.string.noble_vip)
                }
                mBinding.tvVipDesc.text = getString(R.string.renew_vip_again)
                mBinding.tvActivate.apply {
                    text = getString(R.string.activate_membership_now)
                    setBackgroundResource(R.drawable.shape_mine_vip_card_expired_button)
                    setTextColor(ContextCompat.getColor(requireContext(), R.color.color_7B7B7B))
                }
            } else if (it.isVIP) {
                mBinding.llVip.setBackgroundResource(R.mipmap.ic_mine_vip_card)
//                mBinding.labelVip.setLevelInfo("", "1")
                mBinding.tvVipTitle.apply {
                    setDrawableLeft(R.mipmap.ic_diamond, 5)
                    text = getString(R.string.noble_vip)
                }
                mBinding.tvVipDesc.text =
                    getString(R.string.vip_valid_until) + ": " + it.user_vip?.expire_at?.formatDateTime()
                mBinding.tvActivate.apply {
                    text = getString(R.string.renew_membership_now)
                    setBackgroundResource(R.drawable.shape_mine_vip_card_button)
                    setTextColor(ContextCompat.getColor(requireContext(), R.color.color_white))
                }
            } else {
                mBinding.llVip.setBackgroundResource(R.mipmap.ic_mine_vip_card)
                mBinding.tvVipTitle.apply {
                    setCompoundDrawables(null, null, null, null)
                    text = getString(R.string.mine_vip)
                }
                mBinding.tvActivate.apply {
                    text = getString(R.string.activate_membership_now)
                    setBackgroundResource(R.drawable.shape_mine_vip_card_button)
                    setTextColor(ContextCompat.getColor(requireContext(), R.color.color_white))
                }
            }

//            mBinding.tvDiamond.text = it?.diamond.toString()
        }

        mBinding.tvDiamondValue.setContent {
            HeartMergeTheme {
                DiamondComposeView(color = color_590C92, fontSize = 20.sp)
            }
        }

        mBinding.root.addView(
            CheckInView(requireContext()).apply {
                click {
                    showUserSignPopup(requireActivity() as AppCompatActivity)
                }
            }, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT
            )
        )
        mBinding.tvLikeNum.setContent {
            HeartMergeTheme {
                DailyGrowingNumber()
            }
        }

        FlowBus.with<Boolean>(Constants.PushCmd.PUSH_CMD_REMOTE_USER_TASK_FINISH).register(this) {
            if (ActivityManager.current is TaskActivity) return@register
            mBinding.tvTaskStatus.makeVisible()
        }
    }

    override fun bindListener() {
        mBinding.clPerson.click {
            jump(ProfileActivity::class.java)
        }

        mBinding.ivAvatar.click {
            jump(ProfileActivity::class.java)
        }

        //等级中心
        mBinding.levelCardView.click {
            jump(LevelActivity::class.java)
        }

        //签到
        mBinding.tvCheckIn.click {
            MMKVDataRep.lastSignDate = ""
            showUserSignPopup(requireActivity() as AppCompatActivity)
        }

        //会员中心
        mBinding.llVip.click {
            jump(MembershipCenterActivity::class.java)
        }

        // 钱包
        mBinding.llWallet.click {
            jump(WalletActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.Mine)
            })
        }

        mBinding.llWhoSeeMe.click {
            jump(WhoSeeMeActivity::class.java)
        }
        //签到
        mBinding.llDailyAttendance.click {
            MMKVDataRep.lastSignDate = ""
            showUserSignPopup(requireActivity() as AppCompatActivity)
        }

        // 我的匹配卡
        mBinding.llMatchingCard.click { jump(MatchingCardActivity::class.java) }

        // 转盘抽奖
        mBinding.llTurntableRaffle.click {
            jump(RaffleActivity::class.java)
        }

        // 我的背包
        mBinding.llBackpack.click { jump(BackpackActivity::class.java) }

        //分享
        mBinding.llShareFriend.click {
            jump(InvitationActivity::class.java)
        }

        //设置
        mBinding.llSetting.click {
            jump(SettingActivity::class.java)
        }

        mBinding.ivDiamondCard.click {
            jump(WalletActivity::class.java, Bundle().apply {
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.Mine)
            })
        }

        mBinding.tvId.click {
            StringUtil.copy(requireContext(), mBinding.tvId.tag.toString())
            toast(getString(R.string.user_id_already_copy))
        }

        mBinding.ivVipCard.click {
            jump(MembershipCenterActivity::class.java, Bundle().apply {
                putIndex(MMKVDataRep.userInfo.user_vip?.level?.takeIf { it > 0 }?.let { it - 1 }
                    ?: 0)
                putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.Mine)
            })
        }

        mBinding.clRewardTask.click {
            jump(TaskActivity::class.java)
        }

        mBinding.llMenuTask.click {
            jump(TaskActivity::class.java)
            mBinding.tvTaskStatus.makeGone()
        }

        //客服
        mBinding.llMenuCustomer.click {
            WebViewActivity.jump(
                requireContext(),
                title = getString(R.string.mine_menu_customer_service),
                RequestParametersBuilder.appendToUrl("${Constants.Agreement.FAQ_URL}?userId=${MMKVDataRep.userInfo.id}")
            )
        }

        //钻石
        mBinding.llMenuHistory.click {
            jump(DiamondDetailActivity::class.java)
        }

        //游客登录绑定第三方帐号
        mBinding.llMenuSign.click {
            jump(LoginActivity::class.java)
        }

        //设置
        mBinding.llMenuSetting.click {
            jump(SettingActivity::class.java)
        }
    }
}