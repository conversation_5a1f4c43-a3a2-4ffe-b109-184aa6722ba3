package com.heart.heartmerge.ui.activities.anchor

import android.media.MediaPlayer
import android.util.DisplayMetrics
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import android.view.View.OnTouchListener
import android.widget.ImageView
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import coil.load
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.mvi.observeState
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoMessageBean
import com.heart.heartmerge.databinding.ActivityIncomingVideoBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.manager.DownloadVideoManager
import com.heart.heartmerge.manager.GiftManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.DiamondRechargePopup
import com.heart.heartmerge.popup.NormalNewPopup
import com.heart.heartmerge.popup.showDiamondRechargePopup
import com.heart.heartmerge.popup.showGiftPopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.popup.showReportPopup
import com.heart.heartmerge.ui.fragments.home.VideoMessageItem
import com.heart.heartmerge.ui.widget.floatwindow.CallFloatWindow
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.Constants.VIDEO_DEFAULT_EXIT_TIME
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.RongMessageUtil
import com.heart.heartmerge.utils.fromJson
import com.heart.heartmerge.viewmodes.AnchorVideoPageState
import com.heart.heartmerge.viewmodes.AnchorVideoStatus
import com.heart.heartmerge.viewmodes.AnchorVideoViewModel
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.heart.heartmerge.manager.DownloadVideoManager.DownloadListener
import com.shuyu.gsyvideoplayer.player.IjkPlayerManager
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.BeautyOptions
import io.agora.rtc2.video.VideoCanvas
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import tv.danmaku.ijk.media.player.IjkMediaPlayer
import java.io.File


/**
 * 作者：Lxf
 * 创建日期：2024/7/31 18:16
 * 描述：
 */
class IncomingVideoActivity :
    BaseCoreActivity<ActivityIncomingVideoBinding, AnchorVideoViewModel>() {
    private var anchorInfo: UserBean? = null

    private var playSource: String = ""

    private val recordId: Int get() = intent.getIntExtra(Constants.INTENT_PARAM_RECORD_ID, 0)
    private val videoDuration: Int get() = intent.getIntExtra(Constants.INTENT_PARAM_VIDEO_DURATION, 0)
    private val thumbUrl: String get() = intent.getStringExtra("thumb_url") ?: ""
    private val anchorViewModel by viewModels<AnchorViewModel>()

    private val isFromMatch: Boolean
        get() = intent.getBooleanExtra(Constants.INTENT_PARAM_FROM_MATCH, false)

    private val isNeedMute: Boolean
        get() = intent.getBooleanExtra(Constants.INTENT_PARAM_ISNEEDMUTE, false)
    private val isNeedBal: Boolean
        get() = intent.getBooleanExtra(Constants.INTENT_PARAM_ISNEEDBAL, false)
    private val videoPrice: Int
        get() = intent.getIntExtra(Constants.INTENT_PARAM_VIDEO_PRICE, Constants.VIDEO_DEFAULT_PRICE)


    // 填写声网控制台中获取的 App ID
    private var appId = "7223fe6aa34548ecae689a1647f0d243"
    private var mRtcEngine: RtcEngine? = null
    private var videoTimeJob: Job? = null //视频时长 每秒更新
    private var answerCountDownJob: Job? = null //接听倒计时
    private var seconds = 0
    private var exitTipPopupView: NormalNewPopup? = null
    private var localSelVideoCanvas: VideoCanvas? = null
    private val timeString: StringBuilder = StringBuilder()
    private var giftPopup: BasePopupView? = null
    private var rechargeCountDownJob: Job? = null//充值倒计时
    private var mediaPlayer: MediaPlayer? = null
    private var mIsExceptionFinish = false
    private var followed: Boolean = false //是否关注
    private var muteLocalVideoStream = false //true 取消发送本地视频流 false（默认）发送本地视频流
    private var muteLocalAudioStream = false //true取消发布 false（默认）发布
    private var diamondRechargePopup: DiamondRechargePopup? = null
    private var alreadyInsertCallMessage = false
    private var clickListen = false //aiv点击了接听

    override fun getLayoutId(): Int = R.layout.activity_incoming_video
    override fun isImmerse(): Boolean = true
    override fun initView() {
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
        setAnchorInfoBgHeight()
        initActionView()
        initRemoteVideoCloseView()
        mediaPlayer = AppUtil.startRing(this)
        // 获取 OnBackPressedDispatcher 并注册回调
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                showExitPopup()
            }
        })

        mBinding.remoteVideoViewContainerFl.apply {
            setOnTouchListener(object : OnTouchListener {
                private var lastX = 0f
                private var lastY = 0f
                private var dX = 0f
                private var dY = 0f
                private var isDragging = false
                private val screenWidth = resources.displayMetrics.widthPixels
                private val screenHeight = resources.displayMetrics.heightPixels

                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            // 记录手指按下时的坐标
                            lastX = event.rawX
                            lastY = event.rawY
                            dX = v.x - lastX
                            dY = v.y - lastY
                            isDragging = false
                        }

                        MotionEvent.ACTION_MOVE -> {
                            // 计算手指的移动距离并更新视图的位置
                            var newX = event.rawX + dX
                            var newY = event.rawY + dY

                            // 限制视图的移动范围
                            val viewWidth = v.width
                            val viewHeight = v.height

                            // 防止移动到屏幕外
                            newX = newX.coerceIn(0f, (screenWidth - viewWidth).toFloat())
                            newY = newY.coerceIn(0f, (screenHeight - viewHeight).toFloat())

                            // 更新View的位置
                            v.x = newX
                            v.y = newY

                            // 标记拖动动作
                            isDragging = true
                        }

                        MotionEvent.ACTION_UP -> {
                            // 手指抬起时，判断是否为拖动，若不是，则是点击
                            if (!isDragging) {
                                // 处理点击
                                v.performClick()
                            }
                        }
                    }
                    return true
                }
            })
        }
    }

    private fun setAnchorInfoBgHeight() {
        // 获取屏幕的高度
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val screenHeight = displayMetrics.heightPixels

        // 计算屏幕高度的69%
        val targetHeight = (screenHeight * 0.69).toInt()

        // 设置图片的高度
        val layoutParams = mBinding.anchorImage.layoutParams
        layoutParams.height = targetHeight
        mBinding.anchorImage.layoutParams = layoutParams
    }

    override fun onResume() {
        super.onResume()
        AppUtil.canAutoPopupVideoCallingPage = false
    }

    private fun initRemoteVideoCloseView() {
        mBinding.ivRemoteCloseVideoHeader.loadAvatar(MMKVDataRep.userInfo.avatar.buildImageUrl())
    }

    override fun initData() {
        playSource = intent.getStringExtra(Constants.INTENT_PARAM_VIDEO_URL) ?: ""
//        playSource = "https://s3.idim888.com/wetop/2025062320134654227894.mp4"
        anchorInfo = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO)?.fromJson<UserBean>()
        LogX.e("$TAG, playSource: $playSource,  video duration: $videoDuration")
        MMKVBaseDataRep.agoraRtcAppId?.let {
            appId = it
        }
        refreshAnchorInfoView(anchorInfo)
        anchorInfo?.id?.let {
            anchorViewModel.fetchAnchorDetail(it)
        }

        if (isFromMatch) {
            mBinding.anchorListen.makeGone()
            mBinding.tvConnecting.makeVisible()
//            startPlayVideo()
        }
//        } else {
        downloadVideoOrPlayVideo()
        countDownAnswer()
//        }
    }

    private fun downloadVideoOrPlayVideo() {
        if (playSource.startsWith("http")) {
            LogX.e("$TAG, start download video: $playSource")
            DownloadVideoManager.enqueueDownload(IncomingVideoActivity::class.java.simpleName, playSource, object : DownloadListener {
                override fun onStart(url: String) {
                    LogX.d("$TAG, Download started: $url")
                }

                override fun onProgress(url: String, currentOffset: Long, totalLength: Long, progress: Int) {
                    LogX.d("$TAG, Download progress: $url, $progress%")
                    // 可以更新UI显示下载进度
                }

                override fun onComplete(url: String, localPath: String) {
                    LogX.e("$TAG, download video success: $localPath")
                    playSource = localPath
                    MMKVDataRep.cacheFilePath = playSource
                    if (isFromMatch || clickListen) {
                        startPlayVideo()
                    }
                }

                override fun onError(url: String, error: String) {
                    LogX.e("$TAG, download video error: $error")
                    ToastUtil.show(getString(R.string.tip_no_answer))
                    finish()
                }

                override fun onCancel(url: String) {
                    LogX.d("$TAG, Download canceled: $url")
                    ToastUtil.show(getString(R.string.tip_no_answer))
                    finish()
                }
            })
        } else {
            MMKVDataRep.cacheFilePath = playSource
            if (isFromMatch || clickListen) {
                startPlayVideo(true)
            }
        }
    }

    private fun refreshAnchorInfoView(anchorInfo: UserBean?) {
        anchorInfo?.run {
            mBinding.run {
                anchorNameCenter.text = nickname
                anchorNameTop.text = nickname
                val showAvatar = avatar.buildImageUrl()
                anchorHeaderTop.loadAvatar(showAvatar.buildImageUrl())
                anchorHeaderCenter.loadAvatar(showAvatar.buildImageUrl())
                anchorImage.loadAnchorImage(showAvatar.buildImageUrl(highQuality = true))
                anchorAge.text = "$age"
                anchorAgeTop.text = "$age"
                anchorCountry?.title?.takeIf { it.isNotEmpty() }?.let {
                    anchorCountryCenter.makeVisible()
                    anchorCountryTop.makeVisible()
                    anchorCountryCenter.text = it
                    anchorCountryTop.text = it
                }
                tvDiamondCenter.text =
                    String.format(
                        getString(R.string.label_diamond_every_min),
                        (anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                    )

                llVipPrice.makeVisible(anchorLevel?.discountPrice != anchorLevel?.minDiscountPrice)
                tvDiamondVipCenter.text = String.format(
                    getString(R.string.label_diamond_every_min),
                    (anchorLevel?.minDiscountPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                )

                followed = (showRelation == Constants.FOLLOW_FLAG_FOLLOWED || showRelation == Constants.FOLLOW_FLAG_FOLLOWED_EACH_OTHER)
                setFollowView()
            }
        }
    }

    private fun initActionView() {
        mBinding.callingSwitchCamera.click {
            mRtcEngine?.switchCamera()
        }
        mBinding.callingSwitchVideo.run {
            click {
                muteLocalVideoStream = !muteLocalVideoStream
                setImageResource(if (muteLocalVideoStream) R.mipmap.ic_video_view_close else R.mipmap.ic_video_view_open)
                mRtcEngine?.muteLocalVideoStream(muteLocalVideoStream)
                mBinding.viewHideRemote.makeVisible(muteLocalVideoStream)
                mBinding.ivRemoteCloseVideoHeader.makeVisible(muteLocalVideoStream)
            }
        }
        mBinding.callingSwitchAudio.apply {
            click {
                muteLocalAudioStream = !muteLocalAudioStream
                setImageResource(if (muteLocalAudioStream) R.mipmap.ic_video_audio_close else R.mipmap.ic_video_audio_open)
                mRtcEngine?.muteLocalAudioStream(muteLocalAudioStream)
            }
        }

        mBinding.cancelCall.click {
            showExitPopup()
        }
        mBinding.giftSend.click {
            giftPopup = giftPopup?.show() ?: showGiftPopup(this, anchorInfo?.id) { gift ->
                mViewModel.giftGive(anchorId = anchorInfo?.id ?: "0", giftBean = gift)
//                GiftManager.getInstance(this).playGiftAnimation(this, this, gift.giftSvgaUrl)
            }
        }
        mBinding.flClose.click {
            showExitPopup()
        }
        mBinding.flPlatform.click {
//            XPopup.Builder(this).asCustom(PlatformRulePopup(this)).show()
            anchorInfo?.id?.let { string ->
                showReportPopup(this, string)
            }
        }
        mBinding.ivSendMessage.click {
            showDiamondRechargeDialog()
        }
        mBinding.btnRecharge.click {
            showDiamondRechargeDialog()
        }
        mBinding.llTopFollow.click {
            followAction()
        }
        mBinding.clFollow.click {
            followAction()
        }

        mBinding.ivFreeTag.makeVisible(!isNeedBal)

        mBinding.anchorListen.click {
            clickListen = true
            mBinding.anchorListen.makeGone()
            mBinding.tvConnecting.makeVisible()
            if (isNeedBal) {
                if (MMKVDataRep.userInfo.diamond < videoPrice) {
                    showDiamondRechargeDialog()
                } else {
                    listenClickStartPlayVideoLogic()
                }
            } else {
                listenClickStartPlayVideoLogic()
            }
        }
    }

    private fun listenClickStartPlayVideoLogic() {
        if (playSource.startsWith("http")) {
            //说明没有下载完成
            LogX.i("$TAG, 视频仍在下载中: $playSource")
            //点击了接通重新计时
            answerCountDownJob?.cancel()
            answerCountDownJob = null
            countDownAnswer()
        } else {
            startPlayVideo(true)
        }
    }

    private fun startPlayVideo(needDelay: Boolean = false) {
        LogX.i("$TAG, start play video: $playSource")
        answerCountDownJob?.cancel()
        answerCountDownJob = null
        releaseRing()
        AppUtil.requestVideoPermission(this, onGrantedCallBack = {
            lifecycleScope.launch {
                mBinding.anchorListen.makeGone()
                mBinding.cancelCall.makeGone()
                if (needDelay) {
                    delay(2000)
                }
                updateActionView()
                renderVideoView()
                //显示本地视图
                initializeAndPreview()
                updateTimeView()
                MMKVDataRep.cacheFilePath = ""
                mViewModel.aivStart(recordId, if (isFromMatch) 1 else 2)
            }
        }) {
            exceptionFinish()
        }
    }

    private var needFinish = false
    private fun showDiamondRechargeDialog() {
        if (diamondRechargePopup == null) {
            diamondRechargePopup = showDiamondRechargePopup(
                this, true, purchaseScene = if (isFromMatch) PurchaseScene.Match(
                    anchorId = anchorInfo?.id ?: "", videoId = anchorInfo?.virVideoId
                ) else PurchaseScene.VirtualVideo(
                    anchorId = anchorInfo?.id ?: "", videoId = anchorInfo?.virVideoId
                )
            ) {
                if (needFinish) {
                    finish()
                }
            }
        } else {
            diamondRechargePopup?.show()
        }
    }

    private fun countDownAnswer() {
        //30s倒计时 没有接通的话 自动退出
        var count = VIDEO_DEFAULT_EXIT_TIME
        answerCountDownJob =
            mViewModel.countDownCoroutines(VIDEO_DEFAULT_EXIT_TIME, onTick = { time ->
                count--
            }, onFinish = {
                if (count <= 0) { //倒计时结束
                    DownloadVideoManager.cancelDownload(playSource)
                    ToastUtil.show(getString(R.string.tip_no_answer))
                    mViewModel.aivRefuse(recordId, 2, 1)
                    finish()
                }
            })
    }

    private fun updateTimeView() {
        if (playSource.isNotEmpty()) {
            if (isNeedMute) {
                mBinding.rechargeFl.visibility = View.VISIBLE
            }
//            mBinding.rechargeFl.visibility = View.VISIBLE
//            if (isFromMatch) {
//                mBinding.rechargeFl.visibility = View.GONE
//            }
            mBinding.videoTime.visibility = View.VISIBLE
//            val videoDuration = mViewModel.getVideoOrAudioDuration(playSource)
            if (videoDuration > 0) {
                mBinding.customProgressView.visibility = View.VISIBLE
                mBinding.customProgressView.startProgressAnimation(videoDuration * 1000.toLong())
                rechargeCountDownJob = mViewModel.countDownCoroutines(
                    videoDuration,
                    onTick = { time ->
                        if (!isNeedMute && time == 10) {
                            mBinding.rechargeFl.visibility = View.VISIBLE
                        }
                        mBinding.rechargeCountTime.text =
                            String.format(getString(R.string.tip_call_end), time)
                        if (timeString.isNotEmpty()) {
                            timeString.clear()
                        }
                        appendTimeStr(seconds / 3600, false)
                        appendTimeStr((seconds % 3600) / 60, true)
                        val secShow = seconds % 60
                        val secStr = if (secShow > 9) secShow % 60 else "0$secShow"
                        timeString.append(secStr)
                        mBinding.videoTime.text = timeString
                        if (CallFloatWindow.instance.isShowing) {
                            CallFloatWindow.instance.setTimeContent(timeString.toString())
                        }
                        seconds++
                    },
                    onFinish = {
                        finishPlayVideo()
                        insertCustomVideoCallMessage()
                        needFinish = true
                        showDiamondRechargeDialog()
//                        reportVideoRecord(videoDuration)
                    })
            } else {
                LogX.e("error : file duration is 0 ")
            }
        }
    }

    private fun finishPlayVideo() {
        mBinding.videoPlayer.makeGone()
        mBinding.remoteVideoViewContainerFl.makeGone()
        mBinding.flPlatform.makeGone()
        mBinding.flClose.makeGone()
        mBinding.topAnchorInfo.makeGone()
//        mBinding.tvFreeTag.makeGone()
        mBinding.videoCallAction.makeGone()
        mBinding.rechargeFl.makeGone()
        mBinding.videoCallingAction.makeGone()
        mBinding.giftSend.makeGone()
    }

    private fun renderVideoView() {
        //ijk关闭log
        IjkPlayerManager.setLogLevel(IjkMediaPlayer.IJK_LOG_SILENT)
        //全屏
        GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)
        // 设置播放器回调监听器，监听缓冲进度
        mBinding.videoPlayer.apply {
            setBottomProgressBarDrawable(null) // 设置为null可以隐藏底部的进度条
            setUp(playSource, false, "")
            if (thumbUrl.isNotEmpty()) {
                val thumbImageView = ImageView(context).apply {
                    setScaleType(ImageView.ScaleType.CENTER_CROP)
                    load(thumbUrl)
                }
                setThumbImageView(thumbImageView) //增加封面
            }
            hideAllView()
            setIsTouchWigetFull(false)
            setIsTouchWiget(false) //是否可以滑动调整
            if (playSource.isNotEmpty()) {
                startPlayLogic()
            }
        }
        GSYVideoManager.instance().isNeedMute = isNeedMute
        if (isNeedMute) { //isNeedMute 为true，说明希望用户听到声音，也就是视频本身有声音
            mBinding.labelInsufficientDiamonds.text = getString(R.string.lable_unlock_call)
            mBinding.btnRecharge.text = getString(R.string.unlock)
        } else {
            mBinding.labelInsufficientDiamonds.text = getString(R.string.insufficient_diamond_balance)
            mBinding.btnRecharge.text = getString(R.string.recharge)
        }
    }

    private fun appendList(messages: MutableList<VideoMessageBean>) {
        mBinding.messageList.append<VideoMessageItem>(messages) { data ->
            videoMessageBean = data as VideoMessageBean
        }
        mBinding.messageList.smoothScrollToPosition(mBinding.messageList.dslAdapter.itemCount - 1)
    }

    override fun initViewStates() {
        mViewModel.viewStates.let { states ->
            states.observeState(this, AnchorVideoPageState::anchorVideoStatus) {
                when (it) {
                    is AnchorVideoStatus.GiftGiveSuccess -> {
                        DiamondChangeManager.reduceDiamond(it.giftBean.coin)
                        appendList(
                            mutableListOf(
                                VideoMessageBean(
                                    giftImage = it.giftBean.icon, giftCount = it.giveNum
                                )
                            )
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    override fun initViewEvents() {
        mViewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {}
            }
        }

        anchorViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.AnchorFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = false
                    setFollowView()
                }

                is SearchRequestEvent.AnchorUnFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = true
                    setFollowView()
                }

                is SearchRequestEvent.GetAnchorDetailSuccess -> {
                    anchorInfo = it.userBean
                    refreshAnchorInfoView(it.userBean)
                    //用户信息有修改 更新数据IM用户信息
                    RongMessageUtil.refreshCacheUserInfo(it.userBean)
                }

                else -> {}
            }
        }
    }

    //接口异常退出
    private fun exceptionFinish() {
        mIsExceptionFinish = true
        finish()
    }

    private fun releaseRing() {
        mediaPlayer?.release()
        mediaPlayer = null
    }

    /**
     * 初始化视频引擎显示本地视图
     */
    private fun initializeAndPreview() {
        try {
            // 创建并初始化 RtcEngine
            val config = RtcEngineConfig()
            config.mContext = baseContext
            config.mAppId = appId
            mRtcEngine = RtcEngine.create(config)
        } catch (e: Exception) {
            throw RuntimeException("Check the error.")
        }
        mRtcEngine?.apply {
            // 启用视频模块
            enableVideo()
            // 创建一个 SurfaceView 对象，并将其作为 FrameLayout 的子对象
            val surfaceView = SurfaceView(baseContext)
            mBinding.remoteVideoViewContainer.addView(surfaceView)
            // 将 SurfaceView 对象传入声网实时互动 SDK，设置本地视图
            localSelVideoCanvas = VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, 0)
            setupLocalVideo(localSelVideoCanvas)
            // 开启本地预览
            startPreview()
            setBeautyEffectOptions(true, BeautyOptions())
        }
    }

    private fun appendTimeStr(time: Int, always: Boolean) {
        if (time > 9) {
            timeString.append("$time:")
        } else if (time > 0) {
            timeString.append("0$time:")
        } else {
            if (always) {
                timeString.append("00:")
            }
        }
    }

    private fun updateActionView() {
        mBinding.videoCallAction.makeGone()
        mBinding.callAnchorInfo.makeGone()
        mBinding.tvConnecting.makeGone()
        mBinding.videoCallingAction.makeVisible()
        mBinding.giftSend.makeVisible()
        mBinding.messageList.makeVisible()
        mBinding.remoteVideoViewContainerFl.makeVisible()
        mBinding.videoPlayer.makeVisible()
        mBinding.anchorHeaderCenter.makeGone()
        mBinding.topAnchorInfo.makeVisible()
//        mBinding.tvFreeTag.makeVisible()
        mBinding.flClose.makeVisible()
        mBinding.flPlatform.makeVisible()
    }


    private fun insertCustomVideoCallMessage() {
        if (alreadyInsertCallMessage) {
            return
        }
        alreadyInsertCallMessage = true
        if (seconds > 0) { //已经开始播放了
            mViewModel.aivRecord(recordId, seconds)
        }
        anchorInfo?.apply {
            if (timeString.isNotEmpty()) {
                if (isFromMatch) {
                    RongMessageUtil.insertOutgoingVideoMessage(id, timeString.toString())
                } else {
                    RongMessageUtil.insertIncomingVideoMessage(id, timeString.toString())
                }
            }
        }
    }

    private fun showExitPopup() {
        if (isFromMatch) {
            showNormalExitPopup()
        } else {
            if (exitTipPopupView == null) {
                exitTipPopupView = showNormalNewPopup(
                    this,
                    R.mipmap.ic_dialog_call,
                    title = getString(R.string.dialog_title_free_call),
                    content = getString(R.string.dialog_content_free_call),
                    btnSure = getString(R.string.btn_connecting),
                    btnCancel = getString(R.string.btn_hangup),
                    mainColor = R.color.color_EC12E2,
                    cancelBlock = {
                        if (seconds <= 0) { //视频没有播放  说明拒绝了
                            mViewModel.aivRefuse(recordId, 2, 0)
                        }
                        insertCustomVideoCallMessage()
                        finish()
                    })
            } else {
                XPopup.Builder(this).asCustom(exitTipPopupView).show()
            }
        }
    }

    private fun showNormalExitPopup() {
        if (exitTipPopupView == null) {
            exitTipPopupView = showNormalNewPopup(
                this,
                R.mipmap.ic_dialog_call,
                title = getString(R.string.btn_hangup),
                content = getString(R.string.dialog_content_hang_up),
                btnSure = getString(R.string.btn_connecting),
                btnCancel = getString(R.string.btn_hangup),
                mainColor = R.color.color_EC12E2,
                cancelBlock = {
                    insertCustomVideoCallMessage()
                    finish()
                })
        } else {
            XPopup.Builder(this).asCustom(exitTipPopupView).show()
        }
    }

    private fun followAction() {
        if (followed) {
            followed = false
            setFollowView()
            anchorInfo?.id?.let {
                anchorViewModel.anchorUnfollow(it)
            }
        } else {
            followed = true
            setFollowView()
            anchorInfo?.id?.let {
                anchorViewModel.anchorFollow(it)
            }
        }
    }

    //设置follow btn ui
    private fun setFollowView() {
        if (followed) {
            val labelCancelFollow = getString(R.string.label_cancel_follow)
            mBinding.tvFollow.text = labelCancelFollow
            mBinding.tvTopFollow.text = labelCancelFollow
            mBinding.followedIv.setImageResource(R.mipmap.ic_follow)
            mBinding.ivTopFollow.setImageResource(R.mipmap.ic_call_top_unfollow)
            mBinding.llTopFollow.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
            mBinding.clFollow.setBackgroundResource(R.mipmap.bg_call_info_unfollow)
        } else {
            val labelFollow = getString(R.string.off_attention)
            mBinding.tvFollow.text = labelFollow
            mBinding.tvTopFollow.text = labelFollow
            mBinding.followedIv.setImageResource(R.mipmap.ic_follow)
            mBinding.ivTopFollow.setImageResource(R.mipmap.ic_call_top_follow)
            mBinding.llTopFollow.setBackgroundResource(R.drawable.shape_call_top_follow_btn_bg)
            mBinding.clFollow.setBackgroundResource(R.mipmap.bg_call_info_follow)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AppUtil.canAutoPopupVideoCallingPage = true
        if (playSource.isNotEmpty()) {
            val file = File(playSource)
            if (file.exists()) {
                file.delete()
            }
        }
        releaseRing()
        rechargeCountDownJob?.cancel()
        videoTimeJob?.cancel() // 停止计时器
        mRtcEngine?.stopPreview()// 停止本地视频预览
        mRtcEngine = null
        RtcEngine.destroy()// 销毁引擎
        GSYVideoManager.releaseAllVideos()
    }
}