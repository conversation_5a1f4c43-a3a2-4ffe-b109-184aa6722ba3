package com.heart.heartmerge.ui.activities

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.CheckOrderBody
import com.heart.heartmerge.databinding.ActivityWebviewBinding
import com.heart.heartmerge.firebase.report.ReportManager
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.OrderManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.popup.showBecomeMembershipPopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.popup.showPaymentSuccessPopup
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.ui.activities.mine.FeedbackActivity
import com.heart.heartmerge.ui.activities.mine.MembershipCenterActivity
import com.heart.heartmerge.ui.activities.mine.WalletActivity
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.viewmodes.BaseViewModel
import com.heart.heartmerge.viewmodes.UserViewModel
import io.rong.imlib.model.Conversation
import kotlinx.coroutines.launch


/**
 * Copyright 2024 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2024/8/29 16:43
 * @description :WebView页面
 */
class WebViewActivity : BaseCoreActivity<ActivityWebviewBinding, BaseViewModel>() {

    companion object {
        fun jump(context: Context, title: String, url: String) {
            context.startActivity(Intent(context, WebViewActivity::class.java).apply {
                putExtra("title", title)
                putExtra("url", url)
            })
        }
    }

    private val title get() = intent.getStringExtra("title")
    private val url get() = intent.getStringExtra("url")
    private val userViewModel by viewModels<UserViewModel>()

    override fun getLayoutId(): Int = R.layout.activity_webview

    // 静态内部类实现WebViewClient，避免内存泄漏
    private class MyWebViewClient(private val activity: WebViewActivity) : WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            activity.mBinding.progressBar.visibility = View.VISIBLE
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            activity.mBinding.progressBar.visibility = View.GONE
        }

        override fun shouldOverrideUrlLoading(
            view: WebView?, request: WebResourceRequest?
        ): Boolean {
            view?.title?.takeIf { it.isNotEmpty() }?.let {
                activity.setToolbarTitle(it)
            }
            request?.url?.let { url ->
                LogX.i("payment result url: $url")
                //支付成功
                if (url.toString().contains("/return")) {
                    ReportManager.log("third pay result url: $url")
                    LogX.i("third pay result url: $url")
                    val orderId = url.getQueryParameter("id")
                    ReportManager.log("third pay result url orderId: $orderId")
                    LogX.i("third pay result url orderId: $orderId")
                    if (orderId != null && orderId.isNotEmpty()) {
                        activity.showLoading("Loading...")
                        OrderManager.checkOrderStatus(
                            body = CheckOrderBody(
                                payOrderId = orderId, payType = null
                            )
                        ) {
                            it?.let { result ->
                                activity.stopLoading()
                                if (result.isPaid) {
                                    activity.userViewModel.refreshUser()
                                    if (result.isSubscribe) {
                                        showBecomeMembershipPopup(activity) {
                                            activity.lifecycleScope.launch {
                                                FlowBus.with<Boolean>(Constants.SUBSCRIBE_SUCCESS)
                                                    .post(result.isPaid)
                                            }
                                            activity.finish()
                                        }
                                    } else {
                                        showPaymentSuccessPopup(activity) {
                                            activity.lifecycleScope.launch {
                                                FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS)
                                                    .post(result.isPaid)
                                            }
                                            activity.finish()
                                        }
                                    }
                                }
                            } ?: run {
                                ToastUtil.show(activity.getString(R.string.payment_processing))
                                activity.finish()
                            }
                        }
                    }
                    //第三方支付失败处理
                    else {
                        ReportManager.log("third pay result failure")
                        LogX.e("third pay result failure $url")
                        if (!BuildConfig.DEBUG) {
                            showNormalNewPopup(
                                activity,
                                R.mipmap.ic_payment_unfinished,
                                title = activity.getString(R.string.payment_status_unfinished_title),
                                content = activity.getString(R.string.payment_status_unfinished_title_content),
                                btnSure = activity.getString(R.string.confirm),
                                btnCancel = "",
                                mainColor = R.color.color_F53D3D,
                                dismissOnBackPressed = false,
                                dismissOnTouchOutside = false,
                                block = {},
                            )
                        }
                    }
                }
                return !(url.scheme == "http" || url.scheme == "https")
            }
            return false
        }
    }

    // 静态内部类实现WebChromeClient，避免内存泄漏
    private class MyWebChromeClient(private val activity: WebViewActivity) : WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            activity.mBinding.progressBar.progress = newProgress
        }
    }

    override fun initView() {
        super.initView()
        setToolbarTitle(title ?: "")

        // 配置WebView设置
        mBinding.webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = true
            displayZoomControls = false
            setSupportZoom(true)
            defaultTextEncodingName = "utf-8"

            mBinding.webView.addJavascriptInterface(
                WebAppInterface(this@WebViewActivity), "AndroidInterface"
            )
        }

        // 设置WebViewClient
        mBinding.webView.webViewClient = MyWebViewClient(this)

        // 设置WebChromeClient来处理JavaScript的alert等
        mBinding.webView.webChromeClient = MyWebChromeClient(this)

        mBinding.webView.loadUrl(url ?: "", if (url?.contains("uid") == true) buildMap {
            put(Constants.TOKEN, MMKVBaseDataRep.token ?: "")
        } else buildMap {})
    }

    // 使用静态内部类避免内存泄漏
    class WebAppInterface(private val activity: WebViewActivity) {
        // 将此方法暴露给 JavaScript 调用
        @JavascriptInterface
        fun handleAction(data: String) {
            if ("recharge".equals(data)) {
                activity.jump(WalletActivity::class.java, Bundle().apply {
                    putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.CustomService)
                })
            }
            if ("feedback".equals(data)) {
                activity.jump(FeedbackActivity::class.java)
            }
            if ("vip".equals(data)) {
                activity.jump(MembershipCenterActivity::class.java, Bundle().apply {
                    putParcelable(MembershipCenterActivity.SCENE, PurchaseScene.CustomService)
                })
            }
            if ("online-customer".equals(data)) {
                activity.jump(MyRongConversationActivity::class.java, Bundle().apply {
                    putString("targetId", Constants.RONG_YUN_ID_CUSTOM_SERVICE)
                    putString(
                        "ConversationType", Conversation.ConversationType.PRIVATE.name
                    )
                })
            }
        }
    }

    override fun onBackPressed() {
        if (mBinding.webView.canGoBack()) {
            mBinding.webView.goBack()
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        // 清理WebView资源，避免内存泄漏
        mBinding.webView.apply {
            stopLoading()
            clearHistory()
            clearCache(true)
            clearFormData()
            clearSslPreferences()
            removeAllViews()
            removeJavascriptInterface("AndroidInterface")
            setWebViewClient(object : WebViewClient() {})
            // 同样不能设置webChromeClient为null
            setWebChromeClient(WebChromeClient())
            destroy()
        }
        super.onDestroy()
    }
}