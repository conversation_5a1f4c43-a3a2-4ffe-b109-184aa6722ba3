package com.heart.heartmerge.ui.adapter

import android.graphics.Paint
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import com.angcyo.dsladapter.DslAdapter
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.angcyo.dsladapter.ItemSelectorHelper
import com.bdc.android.library.base.dslitem.BaseDslItem
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.GoodsBean
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.payment.BillingService
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.ContextHolder
import com.heart.heartmerge.utils.PurchaseScene

class MembershipSubscribeAdapter(
    items: List<MembershipSubscribeItem>
) : DslAdapter(items) {

    init {
        itemSelectorHelper.selectorModel = ItemSelectorHelper.MODEL_SINGLE
    }

    class MembershipSubscribeItem(
        bean: GoodsBean,
        private val purchaseScene: PurchaseScene?,
        val onChange: (GoodsBean) -> Unit = {}
    ) : BaseDslItem<GoodsBean>(R.layout.item_membership_subscribe) {

        private val bronzeGradientColor = intArrayOf(
            "#9656FF".toColorInt(),
            "#9656FF".toColorInt(),
            "#9656FF".toColorInt(),
        )

        private val silverGradientColor = intArrayOf(
            "#B4B3BB".toColorInt(),
            "#B4B3BB".toColorInt(),
            "#B4B3BB".toColorInt(),
        )

        private val goldGradientColor = intArrayOf(
            "#AB8018".toColorInt(),
            "#AB8018".toColorInt(),
            "#AB8018".toColorInt(),
        )

        private val defaultColor = intArrayOf(
            ContextCompat.getColor(ContextHolder.context, com.bdc.android.library.R.color.white_40),
            ContextCompat.getColor(ContextHolder.context, com.bdc.android.library.R.color.white_40),
            ContextCompat.getColor(ContextHolder.context, com.bdc.android.library.R.color.white_40)
        )

        init {
            itemData = bean
        }

        override fun onItemBind(
            itemHolder: DslViewHolder,
            itemPosition: Int,
            adapterItem: DslAdapterItem,
            payloads: List<Any>
        ) {
            super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
            val item = itemData as? GoodsBean

            itemHolder.tv(R.id.tv_name)?.text = "${item?.title}"

            if (item?.isSubscribe == true) {
                itemHolder.view(R.id.rl_container)?.setBackgroundResource(
                    if (itemIsSelected) when (itemPosition) {
                        0 -> R.drawable.shape_subscribe_item_bronze_active_border
                        1 -> R.drawable.shape_subscribe_item_silver_active_border
                        else -> R.drawable.shape_subscribe_item_gold_active_border
                    }
                    else R.drawable.shape_subscribe_item_normal_border
                )

                itemHolder.tv(R.id.tv_label)?.apply {
                    makeVisible()
                    setTextCompatColor(if (itemIsSelected) com.bdc.android.library.R.color.black else com.bdc.android.library.R.color.white_60)
                    setBackgroundResource(
                        if (itemIsSelected) when (itemPosition) {
                            0 -> R.drawable.shape_subscribe_save_bronze_active_label
                            1 -> R.drawable.shape_subscribe_save_silver_active_label
                            else -> R.drawable.shape_subscribe_save_gold_active_label
                        }
                        else R.drawable.shape_subscribe_save_normal_label
                    )
                    text = "+${item.coin.toInt().toShowDiamond()}"
                }
                itemHolder.tv(R.id.tv_origin_price)?.apply {
                    text = "${item.symbol}${item.initial_price}"
                    paintFlags = this.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                }
                itemHolder.tv(R.id.tv_price)?.text =
                    item.googleExtras?.subscriptionOfferDetails?.first()?.pricingPhases?.pricingPhaseList?.first()?.formattedPrice
                        ?: item.formattedPrice

                itemHolder.tv(R.id.tv_diamond_value)?.setTextColor(
                    ContextCompat.getColor(
                        itemHolder.context,
                        if (itemIsSelected) R.color.color_9F2AF8 else com.bdc.android.library.R.color.white_40
                    )
                )

                itemHolder.tv(R.id.tv_name)?.apply {
                    if (itemIsSelected) {
                        when (itemPosition) {
                            0 -> setTextCompatColor(R.color.color_9F8974)

                            1 -> setTextCompatColor(R.color.color_B4B3BB)

                            else -> setTextCompatColor(R.color.color_AB8018)
                        }
                    } else {
                        setTextCompatColor(R.color.color_979797)
                    }
                }

//                itemHolder.tv(R.id.tv_origin_price)?.setTextColor(
//                    ContextCompat.getColor(
//                        itemHolder.context, com.bdc.android.library.R.color.white_40
//                    )
//                )

                itemHolder.tv(R.id.tv_price)?.apply {
                    if (itemIsSelected) {
                        when (itemPosition) {
                            0 -> setTextCompatColor(R.color.color_9F8974)

                            1 -> setTextCompatColor(R.color.color_B4B3BB)

                            else -> setTextCompatColor(R.color.color_AB8018)
                        }
                    } else {
                        setTextCompatColor(R.color.color_979797)
                    }
                }

//                itemHolder.tv(R.id.tv_give_diamond)
//                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_1B1207))

                itemHolder.v<Button>(R.id.btn_amount)?.apply {
                    setBackgroundResource(R.drawable.selector_darkgold_button)
                    setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_F49719))
                }
            } else {
                itemHolder.view(R.id.rl_container)?.setBackgroundResource(R.mipmap.bg_recharge_item)
                itemHolder.img(R.id.iv_icon)?.apply {
                    val resId =
                        AppUtil.getDrawableByName("ic_diamond_recharge_level${itemPosition + 1}")
                    resId?.let {
                        setBackgroundResource(resId)
                    } ?: run { setBackgroundResource(R.mipmap.ic_diamond_recharge_level6) }
                }
                itemHolder.tv(R.id.tv_diamond_value)
                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
                itemHolder.tv(R.id.tv_give_diamond)
                    ?.setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
                itemHolder.v<Button>(R.id.btn_amount)?.apply {
                    setBackgroundResource(R.drawable.selector_primary_button)
                    setTextColor(ContextCompat.getColor(itemHolder.context, R.color.color_white))
                }
            }

//            itemHolder.tv(R.id.tv_label)?.apply {
//                makeVisible(item?.message?.isNotEmpty() == true)
//                text = item?.message
////                if (item?.isSubscribe == true) {
////                    text = item?.discountMessage
////                } else {
////                    text = item?.message
////                }
//            }

            itemHolder.tv(R.id.tv_diamond_value)?.text = if (item?.isSubscribe == true) ""
            else "${item?.coin?.toInt()?.toShowDiamond()} ${
                itemHolder.context.getString(
                    R.string.diamond
                )
            }"
            itemHolder.view(R.id.ll_give_container)?.makeVisible((item?.coin?.toInt() ?: 0) > 0)
            itemHolder.tv(R.id.tv_give_diamond)?.text =
                item?.coin?.toInt()?.toShowDiamond().toString()
            itemHolder.v<Button>(R.id.btn_amount)?.apply {
                text = "${item?.currency} ${item?.price}"
                click {
                    item?.let {
                        BillingService(itemHolder.context as AppCompatActivity).launch(
                            item, purchaseScene = purchaseScene
                        )
                    }
                }
            }
            itemHolder.clickItem {
                updateItemSelect(true)
                item?.let { bean -> onChange(bean) }
            }
        }
    }
}