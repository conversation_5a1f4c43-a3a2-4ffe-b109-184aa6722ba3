package com.heart.heartmerge.utils

import com.heart.heartmerge.mmkv.MMKVDataRep
import java.net.HttpURLConnection.HTTP_UNAUTHORIZED

object Constants {
    val TOKEN = "Authorization"
    val USER = "User"
    const val SOURCE_PAGE_RECOMMEND = "1"
    const val SOURCE_PAGE_POPULAR = "2"
    const val SOURCE_PAGE_NEW = "3"
    const val SOURCE_PAGE_FOCUS = "4"
    const val SOURCE_PAGE_MESSAGE = "5"
    const val SOURCE_PAGE_DETAIL = "6"
    const val SOURCE_TYPE_VIDEO = "1"
    const val SOURCE_TYPE_MATCH = "2"
    const val GENDER_MALE = 1 //男
    const val GENDER_FEMALE = 2 //女
    const val INTENT_PARAM_KEY_ANCHOR_INFO = "anchorInfo"
    const val INTENT_PARAM_KEY_FROM_MATCH = "fromMatch"
    const val INTENT_PARAM_KEY_CHANNEL_ID = "channelId"
    const val INTENT_PARAM_KEY_ANCHOR_UID = "anchorUid"
    const val ROLE_USER = "1" //普通用户角色
    const val ROLE_ANCHOR = "2" //主播角色
    const val PUSH_TYPE_BLACK_ANCHOR = "black_anchor"//拉黑主播
    const val PUSH_TYPE_ANCHOR_REQUEST_GIFT = "3" //索要礼物
    const val PUSH_TYPE_NOTIFY_CALL = "14" //后台推送的视频
    const val PUSH_TYPE_TEXT_MESSAGE = "text_message" //普通文本
    const val PAYMENT_SUCCESS = "payment_success"//支付成功
    const val SUBSCRIBE_SUCCESS = "subscribe_success"//订阅成功
    const val ANCHOR_FILE_TYPE_PHOTO = "1" //查看主播文件图片
    const val ANCHOR_FILE_TYPE_VIDEO = "2"//查看主播文件视频
    const val INTENT_PARAM_VIDEO_SOURCE = "videoSource"
    const val INTENT_PARAM_IS_INCOMING_CALLING = "incomingCall"
    const val INTENT_PARAM_TAB_INDEX = "tab_index"
    const val INTENT_PARAM_VIDEO_URL = "video_url"
    const val INTENT_PARAM_VIDEO_DURATION = "video_duration"

    const val INTENT_PARAM_RECORD_ID = "record_id"
    const val INTENT_PARAM_FROM_MATCH = "from_match"
    const val INTENT_PARAM_ISNEEDMUTE = "isNeedMute"
    const val INTENT_PARAM_ISNEEDBAL = "isNeedBal"
    const val INTENT_PARAM_VIDEO_PRICE = "videoPrice"


    const val VIDEO_SOURCE_NORMAL = 1 //普通通话
    const val VIDEO_SOURCE_MATCH = 2 //匹配通话
    const val VIDEO_SOURCE_INCOMING_AIB = 3//模拟主播来电拨打

    const val STATUS_ONLINE = "1" //在线
    const val STATUS_BUSY = "2" //匹配通话
    const val STATUS_OFFLINE = "3"//离线
    const val STATUS_DO_NOT_DISTURB = "4"//免打扰

    const val FOLLOW_FLAG_FOLLOWED = "1"
    const val FOLLOW_FLAG_UNFOLLOWED = "0"
    const val FOLLOW_FLAG_FOLLOWED_EACH_OTHER = "3" //双向关注

    const val VIDEO_DEFAULT_EXIT_TIME = 30 //默认视频无接听超时时间
    const val VIDEO_DEFAULT_PRICE = 4000 //默认视频消耗的砖石 后台数据都*100
    const val RONG_YUN_ID_SYSTEM = "10000" //系统通知ID
    const val RONG_YUN_ID_CUSTOM_SERVICE = "1234" //客诉通知ID

    const val TOKEN_EXPIRED = "token_expired"

    const val MESSAGE_EXPEND_KEY_BLOCK = "isBlocked"

    object ErrorCode {
        const val USER_NOT_EXIST = 1001
        const val TOKEN_EXPIRED = HTTP_UNAUTHORIZED
    }

    object Agreement {
        val H5_DOMAIN = "https://www.mindmate.vip"

        //用户注册协议
        val REGISTRATION_URL = "${H5_DOMAIN}/heartmerge/UserAgreement.html"

        //隐私政策
        val PRIVACY_URL = "${H5_DOMAIN}/heartmerge/PrivacyPolicy.html"

        val DATA_REMOVE_URL = "${H5_DOMAIN}/heartmerge/dataremove.html"

        val FAQ_URL = "${H5_DOMAIN}/custom-request/index.html"

        //充值协议
        val RECHARGE_URL = "${H5_DOMAIN}/heartmerge/UserRechargeAgreement.html"

        //使用条款
        val TERMS_USE_URL = "${H5_DOMAIN}/heartmerge/UseOfTerms.html"

        //儿童保护协议
        val CHILD_PROTECTION_AGREEMENT_URL = "${H5_DOMAIN}/heartmerge/ChildProtectionAgreement.html"
    }

    object WebSocketParamValue {
        const val CALL_INVITE_CANCEL_REASON_USER_CANCEL = 1 //用户主动取消
        const val CALL_INVITE_CANCEL_REASON_TIMEOUT_CANCEL = 2 //主播未接听取消
        const val CALL_INVITE_CANCEL_REASON_JOIN_FAILED = 3 //加入房间失败（用户）
        const val CALL_HANG_UP_REASON_USER_HUNG_UP = 1 //接通后用户端主动挂断
        const val CALL_HANG_UP_REASON_HUNG_UP_BY_OTHER = -1 //对方挂断导致被动挂断(用户端/主播端)
        const val CALL_HANG_UP_REASON_BANNED_BY_SERVER = 3 //被声网踢出房间(用户端/主播端)
        const val CALL_HANG_UP_REASON_USER_JOIN_TIMEOUT = 4 //主播端15秒对方都没有进入房间被迫挂断
        const val CALL_HANG_UP_REASON_ANCHOR_JOINED_CANCEL_BY_OTHER_CANCEL = 5 //主播端进入房间收到对方取消的长链接
        const val CALL_HANG_UP_REASON_PERMISSION_CANCEL = 6 //权限挂断(主播端)
        const val CALL_HANG_UP_REASON_ANCHOR_JOIN_10_TIMEOUT = 7 //用户端监听对方10s没进入房间
        const val CALL_HANG_UP_REASON_ANCHOR_JOIN_FAILED = 8 //主播端加入房间失败
        const val CALL_HANG_UP_REASON_INSUFFICIENT_DIAMONDS = 9 //余额不足挂断
        const val CALL_STATING_UPDATE_START_TYPE_BACKGROUND = 1 //通话过程中待机
        const val CALL_STATING_UPDATE_START_TYPE_FOREGROUND = 2 //通话过程中取消待机
    }

    object PushCmd {
        val PUSH_CMD_REMOTE_PULL_LOG = "remote_pull_log" //拉取日志
        val PUSH_CMD_REMOTE_CALL_REFUSE = "remote_call_refuse" //用户收到主播的拒绝
        val PUSH_CMD_REMOTE_USER_GOLD_CHANGE = "remote_user_gold_change" //余额变化
        val PUSH_CMD_REMOTE_USER_CLEAN_MATCH = "remote_user_clean_match" //匹配次数变更
        val PUSH_CMD_REMOTE_SOCKET_STATUS_CHANGE = "remote_socket_status_change" //在线变化
        val PUSH_CMD_REMOTE_NOTIFY_WINDOW = "remote_notify_window" //通知(虚拟视频来电/aib)
        val PUSH_CMD_REMOTE_USER_VIP_CHANGE = "remote_user_vip_change" //vip变化
        val PUSH_CMD_REMOTE_USER_TASK_FINISH = "remote_user_task_finish" //用户任务完成
    }

    object ImageSpec {
        var DOMAIN = ""
        var THUMBNAIL = ""
        var LIST = ""
        var HIGH_QUALITY = ""

        fun getDomain(): String? {
            return resolveSpec(DOMAIN, MMKVDataRep.userConfig?.cdn) {
                DOMAIN = it
            }
        }

        fun get(
            thumbnail: Boolean = true, highQuality: Boolean = false, list: Boolean = false
        ): String? {
            return when {
                thumbnail -> resolveSpec(
                    THUMBNAIL, MMKVDataRep.userConfig?.thumb_param
                ) { THUMBNAIL = it }

                highQuality -> resolveSpec(
                    HIGH_QUALITY, MMKVDataRep.userConfig?.detail_photo_param
                ) { HIGH_QUALITY = it }

                list -> resolveSpec(
                    LIST, MMKVDataRep.userConfig?.list_param
                ) { LIST = it }

                else -> null
            }
        }

        private fun resolveSpec(
            current: String?, fallback: String?, assign: (String) -> Unit
        ): String? {
            return if (current.isNullOrEmpty()) {
                fallback?.also { assign(it) }
            } else {
                current
            }
        }
    }
}