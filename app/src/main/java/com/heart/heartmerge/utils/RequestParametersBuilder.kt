package com.heart.heartmerge.utils

import android.os.Build
import androidx.core.net.toUri
import com.bdc.android.library.ktnet.utils.NetKey
import com.bdc.android.library.utils.AppManager
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.mmkv.MMKVDataRep
import java.util.Locale

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/7/9 15:40
 * @description :
 */
object RequestParametersBuilder {

    fun build(): Map<String, String> {
        val params = mutableMapOf<String, String>()
        params[NetKey.KEY_OS_TYPE] = "1"
        params[NetKey.KEY_DEVICE_ID] = UniqueIDUtil.getUniqueID(AppManager.getApplication())
        params[NetKey.KEY_DEVICE_MODEL] = Build.MODEL
        params[NetKey.KEY_OS_VERSION] = Build.VERSION.RELEASE
        params[NetKey.KEY_PACKAGE_NAME] = BuildConfig.APPLICATION_ID
        params[NetKey.KEY_VERSION] = BuildConfig.VERSION_CODE.toString()
        params[NetKey.KEY_LANG] = Locale.getDefault().language
        params[NetKey.KEY_APP_ID] = MMKVDataRep.userInfo.app_id.toString()
        if (MMKVDataRep.userInfo.id.isNotEmpty()) {
            params["uid"] = MMKVDataRep.userInfo.id
        }
        return params
    }

    fun appendToUrl(baseUrl: String): String {
        val uriBuilder = baseUrl.toUri().buildUpon()
        build().forEach { (k, v) -> uriBuilder.appendQueryParameter(k, v) }
        return uriBuilder.build().toString()
    }
}