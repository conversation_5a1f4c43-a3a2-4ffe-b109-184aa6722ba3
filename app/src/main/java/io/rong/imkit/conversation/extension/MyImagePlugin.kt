package io.rong.imkit.conversation.extension

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Build
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.heart.heartmerge.R
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.showChooseMultiImagesPopup
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.ui.activities.message.MyConversationFragment
import com.heart.heartmerge.ui.activities.message.MyRongConversationActivity
import com.heart.heartmerge.utils.Constants
import io.rong.imkit.picture.entity.LocalMedia
import okhttp3.internal.userAgent

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyImagePlugin : MyImagePluginCopy() {
    override fun obtainDrawable(context: Context): Drawable? {
        return ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_message_image, null)
    }

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
        val fragment: MyConversationFragment = currentFragment as MyConversationFragment
        if (MMKVDataRep.userInfo.isVIP || fragment.mTargetId == Constants.RONG_YUN_ID_CUSTOM_SERVICE) {
//            super.onClick(currentFragment, extension, index)
            conversationIdentifier = extension.conversationIdentifier
            currentFragment.activity?.let {
                val activity = it as MyRongConversationActivity
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && ActivityResultContracts.PickVisualMedia.isPhotoPickerAvailable(activity)) {
                    activity.pickImages() { callBackImage ->
                        if (callBackImage.isNotEmpty()) {
                            sendImage(callBackImage)
                        }
                    }
                } else {
                    showChooseMultiImagesPopup(it) { result ->
                        val localMediaList = ArrayList<LocalMedia>()
                        result?.forEach { selMedia ->
                            localMediaList.add(LocalMedia().apply {
                                path = selMedia?.realPath
                                mimeType = selMedia?.mimeType
                                isOriginal = selMedia?.isOriginal == true
                            })
                        }
                        if (localMediaList.isNotEmpty()) {
                            sendImage(localMediaList)
                        }
                    }
                }

            }

        } else {
            fragment.startVipTipAnimation()
        }
    }

}